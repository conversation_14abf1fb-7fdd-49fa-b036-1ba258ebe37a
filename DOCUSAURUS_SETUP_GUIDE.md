# Docusaurus Setup Guide for @cloc/atoms

This guide provides step-by-step instructions to resolve the webpack module resolution error when using @cloc/atoms components in Docusaurus.

## Problem Summary

The error occurs because:
1. @cloc/atoms package uses the `qs` library which depends on Node.js's `util` module
2. Webpack 5 (used by Docusaurus) no longer automatically polyfills Node.js core modules
3. Browser environments don't have access to Node.js modules like `util`

## Solution Options

### Option 1: Configure Docusaurus with Polyfills (Recommended)

This is the quickest solution that doesn't require changes to the @cloc/atoms package.

#### Step 1: Install Required Dependencies

```bash
cd your-docusaurus-project
npm install --save-dev buffer crypto-browserify path-browserify process querystring-es3 stream-browserify util webpack
```

#### Step 2: Update docusaurus.config.js

```javascript
// docusaurus.config.js
const config = {
  // ... your existing config

  plugins: [
    // Custom webpack plugin to handle Node.js polyfills
    function webpackPolyfillPlugin(context, options) {
      return {
        name: 'webpack-polyfill-plugin',
        configureWebpack(config, isServer, utils) {
          if (!isServer) {
            return {
              resolve: {
                fallback: {
                  "util": require.resolve("util/"),
                  "querystring": require.resolve("querystring-es3"),
                  "stream": require.resolve("stream-browserify"),
                  "buffer": require.resolve("buffer"),
                  "process": require.resolve("process/browser"),
                  "path": require.resolve("path-browserify"),
                  "crypto": require.resolve("crypto-browserify"),
                  "fs": false,
                  "net": false,
                  "tls": false,
                  "child_process": false,
                  "os": false,
                },
              },
              plugins: [
                new utils.webpack.ProvidePlugin({
                  process: 'process/browser',
                  Buffer: ['buffer', 'Buffer'],
                }),
              ],
            };
          }
          return {};
        },
      };
    },
  ],

  // Client modules for additional polyfills
  clientModules: [
    require.resolve('./src/polyfills.js'),
  ],

  // ... rest of your config
};
```

#### Step 3: Create Polyfills File

Create `src/polyfills.js`:

```javascript
// src/polyfills.js
if (typeof global === 'undefined') {
  global = globalThis;
}

if (typeof process === 'undefined') {
  global.process = require('process/browser');
}

if (typeof Buffer === 'undefined') {
  global.Buffer = require('buffer').Buffer;
}

if (typeof setImmediate === 'undefined') {
  global.setImmediate = (fn, ...args) => setTimeout(fn, 0, ...args);
  global.clearImmediate = clearTimeout;
}
```

#### Step 4: Use BrowserOnly for Component Previews

Create safe component wrappers:

```jsx
// src/components/ClocComponentPreview.jsx
import React from 'react';
import BrowserOnly from '@docusaurus/BrowserOnly';

export function SafeClocComponent({ children, fallback }) {
  return (
    <BrowserOnly fallback={fallback || <div>Loading...</div>}>
      {() => {
        try {
          return children();
        } catch (error) {
          console.error('Error loading component:', error);
          return <div>Error loading component: {error.message}</div>;
        }
      }}
    </BrowserOnly>
  );
}

export function BasicTimerExample() {
  return (
    <SafeClocComponent fallback={<div>Loading Basic Timer...</div>}>
      {() => {
        const { ClocBasic } = require('@cloc/atoms');
        return <ClocBasic config={{ apiUrl: 'demo' }} />;
      }}
    </SafeClocComponent>
  );
}
```

### Option 2: Use Universal Export (Alternative)

If you prefer to avoid polyfills, use the universal export which has fewer Node.js dependencies:

```jsx
// Instead of
import { ClocBasic } from '@cloc/atoms';

// Use
import { ClocBasic } from '@cloc/atoms/universal';
```

### Option 3: Fix the Package Build (For Package Maintainers)

If you maintain the @cloc/atoms package, update the Rollup configuration:

```javascript
// packages/toolkit/atoms/rollup.config.js
resolve({
  extensions: ['.js', '.ts', '.jsx', '.tsx'],
  preferBuiltins: false, // Changed from true
  browser: true, // Prioritize browser versions
  skip: ['next/link', '@remix-run/react', 'react-router-dom']
}),

// Add Node.js modules to external
external: [
  'react',
  'react-dom',
  'theme-ui',
  // ... other externals
  'util',
  'querystring',
  'stream',
  'buffer',
  'process',
  'path',
  'crypto',
  'fs',
  'net',
  'tls'
],
```

## Testing the Solution

1. Start your Docusaurus development server:
   ```bash
   npm start
   ```

2. Try importing @cloc/atoms components in your documentation:
   ```jsx
   import { BasicTimerExample } from '../src/components/ClocComponentPreview';

   <BasicTimerExample />
   ```

3. Verify that the components load without webpack errors.

## Troubleshooting

### If you still get module resolution errors:

1. Clear your build cache:
   ```bash
   npm run clear
   rm -rf node_modules/.cache
   ```

2. Ensure all polyfill packages are installed:
   ```bash
   npm list buffer crypto-browserify path-browserify process querystring-es3 stream-browserify util
   ```

3. Check that your polyfills.js file is being loaded by adding a console.log:
   ```javascript
   // src/polyfills.js
   console.log('Polyfills loaded');
   ```

### If components don't render properly:

1. Use BrowserOnly wrapper for all @cloc/atoms components
2. Handle errors gracefully with try-catch blocks
3. Provide meaningful fallback content

## Best Practices

1. **Always use BrowserOnly**: Wrap @cloc/atoms components in BrowserOnly to prevent SSR issues
2. **Handle errors gracefully**: Use try-catch blocks when importing components
3. **Provide fallbacks**: Always provide loading states and error messages
4. **Test thoroughly**: Test both development and production builds
5. **Monitor bundle size**: Polyfills can increase bundle size, monitor with webpack-bundle-analyzer

## Additional Resources

- [Docusaurus BrowserOnly documentation](https://docusaurus.io/docs/docusaurus-core#browseronly)
- [Webpack 5 Node.js polyfills](https://webpack.js.org/configuration/resolve/#resolvefallback)
- [Node.js polyfills for browsers](https://github.com/webpack/node-libs-browser)
