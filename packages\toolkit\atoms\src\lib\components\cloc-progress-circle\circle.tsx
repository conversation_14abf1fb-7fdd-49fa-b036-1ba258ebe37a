import { ReactElement } from 'react';

/**
 * Interface representing the color scheme for the ProgressCircle component.
 * @interface IColors
 */
interface IColors {
	/**
	 * The primary color used in the gradient.
	 * @type {string}
	 */
	primary?: string;

	/**
	 * The secondary color used in the gradient.
	 * @type {string}
	 */
	secondary?: string;

	/**
	 * The background color of the progress circle.
	 * @type {string}
	 */
	background?: string;
}

/**
 * Interface for the properties of the ProgressCircle component.
 * @export
 * @interface ProgressCircleProps
 */
export interface ProgressCircleProps {
	/**
	 * The percentage of progress to be displayed (0-100).
	 * @type {number}
	 */
	percentage?: number;

	/**
	 * The radius of the progress circle.
	 * @type {number}
	 */
	radius?: number;

	/**
	 * The width of the circle's stroke (line thickness).
	 * @type {number}
	 */
	strokeWidth?: number;

	/**
	 * The size of the progress circle. This will determine the width and height of the component.
	 * @type {number}
	 */
	size?: number;

	/**
	 * The duration of the animation in milliseconds.
	 * @type {number}
	 */
	duration?: number;

	/**
	 * An optional colors object to customize the gradient and background.
	 * @type {null | IColors}
	 */
	colors?: null | IColors;
}

/**
 * Renders a circular progress indicator with a gradient stroke and dynamic percentage.
 *
 * @export
 * @param {ProgressCircleProps} {
 *   percentage,
 *   radius = 45,
 *   strokeWidth = 10,
 *   duration = 500,
 *   size=60,
 *   colors = {
 *     primary: "var(--primaryColor)",
 *     secondary: "var(--secondaryColor)",
 *     background: "#8e06bb39",
 *   },
 * }
 * @return {JSX.Element} The rendered progress circle component.
 */
export function ProgressCircle({
	percentage,
	radius = 45,
	strokeWidth = 10,
	duration = 500,
	size = 60,
	colors = {
		primary: 'var(--primaryColor)',
		secondary: 'var(--secondaryColor)',
		background: '#8e06bb39'
	}
}: ProgressCircleProps): ReactElement {
	// Calculate the circumference of the circle based on the radius
	const circumference = 2 * Math.PI * radius;
	// Calculate the stroke offset based on the percentage of progress
	const offset = percentage ? circumference - (percentage / 100) * circumference : 0;
	return (
		<div className={`relative h-${size} w-${size}`}>
			<svg className="w-full h-full transform -rotate-90" aria-label="progress">
				<defs>
					{/* Define a linear gradient for the progress stroke */}
					<linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
						<stop offset="0%" stopColor={colors?.primary} /> {/* Starting color */}
						<stop offset="100%" stopColor={colors?.secondary} /> {/* Ending color */}
					</linearGradient>
				</defs>
				{/* Render the background circle */}
				<circle
					cx="50%"
					cy="50%"
					r={radius}
					strokeWidth={strokeWidth}
					stroke={colors?.background}
					fill="transparent"
				/>
				{/* Render the foreground circle representing the progress */}
				<circle
					cx="50%"
					cy="50%"
					r={radius}
					strokeWidth={strokeWidth}
					fill="transparent"
					strokeDasharray={circumference} // Set the dash array to the full circumference
					strokeDashoffset={offset} // Set the dash offset based on the percentage
					stroke="url(#progressGradient)" // Apply the gradient to the stroke
					strokeLinecap="round" // Make the ends of the stroke rounded
					className="transition-all" // Apply transition for smooth animation
					style={{ transitionDuration: `${duration}ms` }} // Set animation duration
				/>
			</svg>
			{/* Display the percentage inside the circle */}
			<div className="absolute inset-0 flex items-center justify-center text-xl font-semibold">{percentage ?? 0}%</div>
		</div>
	);
}
