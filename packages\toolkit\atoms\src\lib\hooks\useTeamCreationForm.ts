import { useState } from 'react';
import { toast } from '@cloc/ui';
import { createTeam, getOrganisationTeams } from '@cloc/api';
import { IUser } from '@cloc/types';
import { useAccessToken } from './useAccessToken';
import { useAtom } from 'jotai';
import { organizationTeamsAtom } from '@lib/cloc-jotai/atoms/cloc-atoms';

interface TeamCreationFormValues {
	teamName: string;
	description?: string;
}

export const useTeamCreationForm = (user: IUser | null, organizationId: string) => {
	const [formValues, setFormValues] = useState<TeamCreationFormValues>({
		teamName: '',
		description: ''
	});

	const { accessToken: token } = useAccessToken();

	const [, setTeams] = useAtom(organizationTeamsAtom);

	const [error, setError] = useState<string>('');
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		setFormValues((prev) => ({
			...prev,
			[e.target.name]: e.target.value
		}));
		setError(''); // Clear error on change
	};

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		setError(''); // Clear previous error

		if (!formValues.teamName || formValues.teamName.trim() === '' || formValues.teamName.length < 3) {
			// Validate team name
			setError('Team name is required and should be at least 3 characters long');
			toast({
				variant: 'destructive',
				description: 'Team name is required and should be at least 3 characters long'
			});
			return;
		}

		try {
			setIsSubmitting(true);
			const createdTeam = await createTeam({
				currentUser: user,
				token,
				organizationId,
				formData: formValues
			});

			if ('message' in createdTeam || 'error' in createdTeam) {
				const errorMessage =
					'message' in createdTeam
						? Array.isArray(createdTeam.message)
							? createdTeam.message.join(', ')
							: createdTeam.message
						: String(createdTeam.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});
				setError(errorMessage);

				return;
			}

			// Fetch updated teams
			setTeams((prev) => ({ ...prev, loading: true }));

			const userTeams = await getOrganisationTeams(user, token, organizationId);

			if ('message' in userTeams || 'error' in userTeams) {
				const errorMessage =
					'message' in userTeams
						? Array.isArray(userTeams.message)
							? userTeams.message.join(', ')
							: userTeams.message
						: String(userTeams.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});
				setError(errorMessage);

				return;
			}

			setTeams({ data: userTeams, loading: false });

			toast({ description: 'Team created successfully!', variant: 'default' });
			resetForm();
		} catch (error) {
			setError((error as Error)?.message || 'Failed to create team');
			toast({
				variant: 'destructive',
				description: (error as Error)?.message || 'Failed to create team'
			});
		} finally {
			setIsSubmitting(false);
			setTeams((prev) => ({ ...prev, loading: false }));
		}
	};

	const resetForm = () => {
		setFormValues({
			teamName: '',
			description: ''
		});
	};

	return {
		error,
		formValues,
		handleChange,
		handleSubmit,
		isSubmitting
	};
};
