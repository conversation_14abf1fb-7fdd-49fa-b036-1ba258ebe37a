import { useEffect, useState, ReactNode } from 'react';
import { Theme, ThemeUIProvider } from 'theme-ui';

import { Provider as Jo<PERSON>Provider } from 'jotai';
import { useAtom } from 'jotai';

import { ChartConfig, ChartData } from '@cloc/types';
import { useAccessToken } from '@hooks/useAccessToken';
import { useAuthUser } from '@hooks/useAuthUser';
import { useTimerStatus } from '@hooks/useTimerStatus';
import { useTimer } from '@hooks/useTimer';
import { useReport } from '@hooks/useReport';
import { useOrganizationTeams } from '@hooks/useOrganisationTeams';
import { useMyTasks } from '@hooks/useMyTasks';
import { useOrganizationProjects } from '@hooks/useOrganisationProjects';
import {
    appliedThemeAtom,
    currentClocStateAtom,
    reportDatesAtom,
    selectedEmployeeAtom,
    selectedTeamAtom
} from '@lib/cloc-jotai/atoms/cloc-atoms';
import useFontSelector from '@hooks/useFontSelector';
import { useEmployeeOrganization } from '@hooks/useEmployeeOrganisation';
import { Toaster } from '@cloc/ui';
import { useUserPermission } from '@hooks/useUserPermission';
import { useMember } from '@hooks/useMember';
import { useStatisticsCounts } from '@hooks/useStatisticsCounts';
import { useProjectsStats } from '@hooks/useProjectsStatistics';
import { useTasksStats } from '@hooks/useTasksStatistics';
import { useActivitiesStats } from '@hooks/useActivitiesStatistics';
import { useUserOrganization } from '@hooks/useUserOrganization';
import { I18nextProvider } from 'react-i18next';
import { changeClocLanguage } from '@components/i18n/language-switch';
import { ClocContext } from '@lib/context/cloc-context';
import i18n from '@lib/i18n/init';
import { useSelectedOrganization } from '@hooks/useSelectedOrganization';
import { createContext } from 'react';

const FrameworkAgnosticThemeContext = createContext<{
    theme?: string;
    setTheme?: (theme: string) => void;
}>({});

const FrameworkAgnosticThemeProvider = ({
    children,
    defaultTheme = 'light'
}: {
    children: ReactNode;
    defaultTheme?: string;
}) => {
    const [theme, setTheme] = useState(defaultTheme);

    useEffect(() => {
        if (typeof document !== 'undefined') {
            document.documentElement.className = theme;
            document.documentElement.setAttribute('data-theme', theme);
        }
    }, [theme]);

    return (
        <FrameworkAgnosticThemeContext.Provider value={{ theme, setTheme }}>
            {children}
        </FrameworkAgnosticThemeContext.Provider>
    );
};

export const sampleChartData: ChartData[] = [
    { day: 'Monday', cedric: 5, salva: 7, josh: 2, ndeko: 7 },
    { day: 'Tuesday', cedric: 8, salva: 4, josh: 7, ndeko: 2 },
    { day: 'Wednesday', cedric: 8, salva: 10, josh: 2, ndeko: 3 },
    { day: 'Thursday', cedric: 5, salva: 11, josh: 9, ndeko: 1 },
    { day: 'Friday', cedric: 13, salva: 5, josh: 13, ndeko: 8 },
    { day: 'Saturday', cedric: 4, salva: 7, josh: 5, ndeko: 3 },
    { day: 'Sunday', cedric: 4, salva: 7, josh: 6, ndeko: 7 }
];

const useClocState = (lang: string) => {
    const [chartConfig, setChartConfig] = useState<ChartConfig>({});
    const [language, setLanguage] = useState(lang);

    const [currentClocState, setCurrentClocState] = useAtom(currentClocStateAtom);
    const [reportDates] = useAtom(reportDatesAtom);
    const [appliedTheme, setAppliedTheme] = useAtom(appliedThemeAtom);
    const [selectedEmployee] = useAtom(selectedEmployeeAtom);
    const [selectedTeam] = useAtom(selectedTeamAtom);

    const { fontOptions, selectedFont, setSelectedFont } = useFontSelector();
    const { accessToken } = useAccessToken();
    const { data: user } = useAuthUser(accessToken);
    const { data: userPermissions } = useUserPermission(user, accessToken);
    const { data: userOrganizations } = useUserOrganization(user, accessToken);
    const organizationId = useSelectedOrganization(user, userOrganizations);
    const { data: timerStatus } = useTimerStatus(user, accessToken, organizationId);

    const {
        start,
        pause,
        isRunning,
        hours,
        totalSeconds,
        minutes,
        seconds,
        startTimer,
        stopTimer,
        setTodayTrackedTime,
        todayTrackedTime
    } = useTimer({
        currentClocState,
        token: accessToken,
        timerStatus: timerStatus ?? { duration: 0, lastLog: null, running: false },
        user,
        organizationId
    });

    const { } = useMember(user, accessToken, userPermissions, organizationId);
    const { } = useReport(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);
    const { } = useStatisticsCounts(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);
    const { } = useActivitiesStats(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);
    const { } = useProjectsStats(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);
    const { } = useTasksStats(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);
    const { } = useEmployeeOrganization(user, accessToken, organizationId);
    const { } = useOrganizationProjects(user, accessToken, currentClocState.clientId, organizationId);
    const { } = useOrganizationTeams(user, accessToken, currentClocState.projectId, organizationId);
    const { } = useMyTasks(user, accessToken, currentClocState.projectId, organizationId);

    useEffect(() => {
        timerStatus && timerStatus.duration && setTodayTrackedTime(new Date(timerStatus.duration * 1000));
        timerStatus &&
            timerStatus.lastLog &&
            setCurrentClocState({
                taskId: timerStatus.lastLog.taskId,
                projectId: timerStatus.lastLog.projectId,
                organizationTeamId: timerStatus.lastLog.organizationTeamId,
                clientId: timerStatus.lastLog.organizationContactId
            });
    }, [timerStatus]);

    useEffect(() => {
        const storedLanguage = localStorage.getItem('preferred-language');
        if (storedLanguage) {
            setLanguage(storedLanguage);
        }
    }, [lang]);

    useEffect(() => {
        changeClocLanguage(language);
    }, [language]);

    return {
        chartConfig,
        setChartConfig,
        language,
        currentClocState,
        appliedTheme,
        setAppliedTheme,
        fontOptions,
        selectedFont,
        setSelectedFont,
        start,
        pause,
        startTimer,
        stopTimer,
        isRunning,
        hours,
        minutes,
        seconds,
        totalSeconds,
        todayTrackedTime
    };
};

const useThemeManager = (theme?: Theme<{}>) => {
    const [, setAppliedTheme] = useAtom(appliedThemeAtom);

    useEffect(() => {
        theme && setAppliedTheme(theme);
    }, [theme, setAppliedTheme]);
};

const ClocContextProvider = ({ children }: { children: ReactNode }) => {
    const clocState = useClocState('en');

    return (
        <ClocContext.Provider
            value={{
                start: clocState.start,
                pause: clocState.pause,
                startTimer: clocState.startTimer,
                stopTimer: clocState.stopTimer,
                isRunning: clocState.isRunning,
                hours: clocState.hours,
                minutes: clocState.minutes,
                seconds: clocState.seconds,
                totalSeconds: clocState.totalSeconds,
                defaultData: sampleChartData,
                config: clocState.chartConfig,
                setConfig: clocState.setChartConfig,
                fontOptions: clocState.fontOptions,
                selectedFont: clocState.selectedFont,
                setSelectedFont: clocState.setSelectedFont,
                todayTrackedTime: clocState.todayTrackedTime
            }}
        >
            {children}
        </ClocContext.Provider>
    );
};

const ClocDataProvider = ({
    children,
    theme,
    defaultTheme = 'light'
}: {
    children?: ReactNode;
    theme?: Theme<{}>;
    defaultTheme?: string;
}) => {
    useThemeManager(theme);

    return (
        <ClocContextProvider>
            <I18nextProvider i18n={i18n}>
                <FrameworkAgnosticThemeProvider defaultTheme={defaultTheme}>
                    <ThemeUIProvider theme={theme || {}}>{children}</ThemeUIProvider>
                </FrameworkAgnosticThemeProvider>
            </I18nextProvider>
        </ClocContextProvider>
    );
};

const ClocProviderCore = ({ children, ...props }: {
    children?: ReactNode;
    theme?: Theme<{}>;
    token?: string;
    lang?: string;
    defaultTheme?: string;
}) => {
    return (
        <JotaiProvider>
            <ClocDataProvider {...props}>{children}</ClocDataProvider>
            <Toaster />
        </JotaiProvider>
    );
};

ClocProviderCore.displayName = 'ClocProviderCore';

export { ClocProviderCore };
export { ClocProviderCore as ClocProvider };
