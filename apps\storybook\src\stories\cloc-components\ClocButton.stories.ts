import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { ClocButton } from '@cloc/atoms';
import { ChevronLeft } from 'lucide-react';
import { ReactNode } from 'react';

const meta = {
	title: 'Clock/Cloc Button',
	component: ClocButton,
	parameters: {
		layout: 'centered'
	},

	// Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
	args: { onClick: fn() }
} satisfies Meta<typeof ClocButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const SmallClocButton: Story = {
	args: {
		variant: 'default',
		size: 'sm'
	}
};

export const SmallStopButton: Story = {
	args: {
		variant: 'default',
		size: 'sm'
	}
};

export const SmallPauseButton: Story = {
	args: {
		variant: 'default',
		size: 'sm'
	}
};

export const DefaultClocButton: Story = {
	args: {
		variant: 'default',
		size: 'default'
	}
};

export const StopButton: Story = {
	args: {
		variant: 'default',
		size: 'default'
	}
};

export const PauseButton: Story = {
	args: {
		variant: 'default',
		size: 'default'
	}
};

export const LargeClocButton: Story = {
	args: {
		variant: 'default',
		size: 'lg'
	}
};

export const LargeStopButton: Story = {
	args: {
		variant: 'default',
		size: 'lg'
	}
};

export const LargePauseButton: Story = {
	args: {
		variant: 'default',
		size: 'lg'
	}
};

export const SmallBorderedPauseButton: Story = {
	args: {
		variant: 'bordered',
		size: 'sm'
	}
};

export const DefaultBorderedPauseButton: Story = {
	args: {
		variant: 'bordered',
		size: 'default'
	}
};

export const LargeBorderedPauseButton: Story = {
	args: {
		variant: 'bordered',
		size: 'lg'
	}
};
