'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { cn } from '@cloc/ui';
import { Button, Calendar, Popover, PopoverContent, PopoverTrigger } from '@cloc/ui';
import { IDatePickerProps } from '@cloc/types';
import { useClocContext } from '@lib/context/cloc-context';

export function ClocDatePicker({ placeholder = 'Pick a date', icon = true, ...props }: IDatePickerProps) {
	const [date, setDate] = React.useState<Date>();
	const { appliedTheme } = useClocContext();

	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button
					variant={'outline'}
					className={cn('w-[280px] justify-start text-left font-normal', !date && 'text-muted-foreground')}
				>
					{icon && <CalendarIcon className="mr-2 h-4 w-4" />}
					{date ? format(date, 'PPP') : <span>{placeholder}</span>}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-auto p-0">
				<Calendar
					modifiersStyles={{
						selected: {
							backgroundColor: (appliedTheme.colors?.borderColor as string) || 'transparent'
						}
					}}
					{...props}
					mode="single"
					selected={date}
					onSelect={setDate}
					initialFocus
				/>
			</PopoverContent>
		</Popover>
	);
}
