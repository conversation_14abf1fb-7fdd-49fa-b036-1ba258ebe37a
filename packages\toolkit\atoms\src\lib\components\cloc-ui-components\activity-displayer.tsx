import { Card, cn, getWeekStartAndEnd, Progress, areDatesEqual } from '@cloc/ui';
import { SpinOverlayLoader } from '../loaders/spin-overlay-loader';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

interface IActivityDisplayerProps {
	activity?: number;
	icon?: React.ReactNode;
	label: string;
	showProgress?: boolean;
	className?: string;
}

const ClocActivityDisplayer: React.FC<IActivityDisplayerProps> = ({
	icon,
	activity = 0,
	label,
	showProgress = true,
	className
}) => {
	const {
		loadings: { statisticsCountsLoading }
	} = useClocContext();
	return (
		<Card
			className={cn(
				'dark:text-white border relative dark:border-gray-600 text-sm rounded-xl p-3 min-w-[150px]  gap-1 inline-flex  flex-col',
				className
			)}
		>
			{statisticsCountsLoading && <SpinOverlayLoader />}
			<div className="flex justify-between items-center dark:text-white/50 text-gray-400">
				<span className="text-xs">{label}</span>
				<span>{icon}</span>
			</div>
			<div className="text-xl font-medium ">{activity}%</div>
			{showProgress && <Progress className="w-full h-2" value={activity} />}
		</Card>
	);
};

const DailyActivityDisplayer = ({ showProgress, className }: { showProgress?: boolean; className?: string }) => {
	const { statisticsCounts } = useClocContext();
	const { t } = useTranslation();
	return (
		<ClocActivityDisplayer
			activity={statisticsCounts?.todayActivities}
			showProgress={showProgress}
			label={t('REPORT.today_activity')}
			className={className}
		/>
	);
};

const WeeklyActivityDisplayer = ({ showProgress, className }: { showProgress?: boolean; className?: string }) => {
	const { statisticsCounts, reportDates } = useClocContext();
	const dates = getWeekStartAndEnd();
	const { t } = useTranslation();
	return (
		<ClocActivityDisplayer
			activity={statisticsCounts?.weekActivities}
			showProgress={showProgress}
			label={
				areDatesEqual(reportDates?.from, dates.start) && areDatesEqual(reportDates?.to, dates.end)
					? t('REPORT.week_activity')
					: t('REPORT.activity_over_period')
			}
			className={className}
		/>
	);
};

export { DailyActivityDisplayer, WeeklyActivityDisplayer, ClocActivityDisplayer };
