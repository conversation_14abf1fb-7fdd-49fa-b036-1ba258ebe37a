import { createContext, SetStateAction, useContext } from 'react';
import {
	IHookResponse,
	IMember,
	IOrganizationContact,
	IOrganizationTeamList,
	IPermission,
	IProject,
	ITeamTask,
	ITimerStatus,
	IUser,
	IUserOrganization,
	IWeeklyReports,
	PaginationResponse
} from '@cloc/types';
import { ChartConfig } from '@cloc/types';
import { Theme } from 'theme-ui';
import { TimeValues } from '../hooks/useClocStopWatch';
import { ICurrentClocState } from '@cloc/types';
import { DateRange } from 'react-day-picker';
import { useAtom } from 'jotai';
import {
	accessTokenAtom,
	activitiesStatsAtom,
	appliedThemeAtom,
	currentClocStateAtom,
	membersAtom,
	organizationClientsAtom,
	organizationProjectsAtom,
	organizationTeamsAtom,
	projectsStatsAtom,
	reportAtom,
	reportDatesAtom,
	selectedEmployee<PERSON>tom,
	selectedFont<PERSON>tom,
	selectedOrganization<PERSON>tom,
	selectedTeam<PERSON>tom,
	statisticsCountsAtom,
	tasks<PERSON>tom,
	tasksStats<PERSON>tom,
	timerLoading<PERSON>tom,
	timer<PERSON>tatus<PERSON><PERSON>,
	user<PERSON>tom,
	userOrganizationsAtom,
	userPermissions<PERSON>tom
} from '../cloc-jotai/atoms/cloc-atoms';
import { FontOption } from '../font/font';
import { IStatisticsCounts } from '@hooks/useStatisticsCounts';
import { IProjectsStats } from '@hooks/useProjectsStatistics';
import { ITasksStats } from '@hooks/useTasksStatistics';
import { IActivitiesStats } from '@cloc/types';

export interface IClocContext {
	start: () => void;
	pause: () => void;
	startTimer: () => Promise<void>;
	stopTimer: () => Promise<void>;
	isRunning: boolean;
	hours: number;
	minutes: number;
	seconds: number;
	totalSeconds: number;
	selectedFont: string;
	fontOptions: FontOption[];
	setSelectedFont: React.Dispatch<React.SetStateAction<string>>;
	defaultData: { [key: string]: string | number }[];
	config: ChartConfig;
	setConfig: React.Dispatch<React.SetStateAction<ChartConfig>>;

	todayTrackedTime: TimeValues;

	handleChangeToken?: {
		handleTokenSubmit: (event: React.FormEvent<HTMLFormElement>) => Promise<void>;
		handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	};
}

export type IReturnUseClocContext = IClocContext & {
	appliedTheme: Theme<{}>;
	setAppliedTheme: React.Dispatch<React.SetStateAction<Theme<{}>>>;

	authenticatedUser: IUser | null;
	setAuthenticatedUser: React.Dispatch<SetStateAction<IHookResponse<IUser>>>;

	token?: string;
	setToken: React.Dispatch<React.SetStateAction<string>>;

	timerStatus: ITimerStatus | null;

	report: IWeeklyReports | null;

	statisticsCounts: IStatisticsCounts | null;

	projectsStats: IProjectsStats | null;

	tasksStats: ITasksStats | null;

	activitiesStats: IActivitiesStats | null;

	organizationClients: IOrganizationContact[] | null;

	reportDates?: DateRange;
	setReportDates?: React.Dispatch<React.SetStateAction<DateRange | undefined>>;

	organizationTeams: PaginationResponse<IOrganizationTeamList> | null;
	tasks: ITeamTask[] | null;

	members: PaginationResponse<IMember> | null;

	organizationProjects: IProject[] | null;

	userPermissions: IPermission[] | null;
	setPermissions: React.Dispatch<React.SetStateAction<IHookResponse<IPermission[]>>>;

	userOrganizations: PaginationResponse<IUserOrganization> | null;

	currentClocState: ICurrentClocState | null;
	setCurrentClocState: React.Dispatch<React.SetStateAction<ICurrentClocState>>;

	selectedEmployee: string;
	setSelectedEmployee: React.Dispatch<SetStateAction<string>>;

	selectedTeam: string;
	setSelectedTeam: React.Dispatch<SetStateAction<string>>;

	selectedOrganization: string;
	setSelectedOrganization: React.Dispatch<SetStateAction<string>>;

	loadings: {
		reportLoading: boolean;
		userLoading: boolean;
		userTasksLoading: boolean;
		organisationsLoading: boolean;
		projectsLoading: boolean;
		timerStatusLoading: boolean;
		clientsLoading: boolean;
		permissionLoading: boolean;
		statisticsCountsLoading: boolean;
		projectsStatsLoading: boolean;
		tasksStatsLoading: boolean;
		activitiesStatsLoading: boolean;
		membersLoading: boolean;
		userOrganizationsLoading: boolean;
	};
	timerLoading: boolean;
	setTimerLoading: React.Dispatch<React.SetStateAction<boolean>>;
};

export const ClocContextDefaultValue: IClocContext | null = null;

const ClocContext: React.Context<IClocContext | null> = createContext<IClocContext | null>(ClocContextDefaultValue);

const useClocContext = (): IReturnUseClocContext => {
	const [accessToken, setAccessToken] = useAtom(accessTokenAtom);
	const [currentClocState, setCurrentClocState] = useAtom(currentClocStateAtom);
	const [reportDates, setReportDates] = useAtom(reportDatesAtom);
	const [appliedTheme, setAppliedTheme] = useAtom(appliedThemeAtom);
	const [{ fontOptions, selectedFont, setSelectedFont }] = useAtom(selectedFontAtom);

	const [timerLoading, setTimerLoading] = useAtom(timerLoadingAtom);
	const [{ data: timerStatus, loading: timerStatusLoading }] = useAtom(timerStatusAtom);
	const [{ data: report, loading: reportLoading }] = useAtom(reportAtom);
	const [{ data: organizationTeams, loading: organisationsLoading }] = useAtom(organizationTeamsAtom);
	const [{ data: organizationProjects, loading: projectsLoading }] = useAtom(organizationProjectsAtom);
	const [{ data: organizationClients, loading: clientsLoading }] = useAtom(organizationClientsAtom);
	const [{ data: userTasks, loading: userTasksLoading }] = useAtom(tasksAtom);
	const [{ data: user, loading: userLoading }, setUser] = useAtom(userAtom);
	const [{ data: userPermissions, loading: permissionLoading }, setPermissions] = useAtom(userPermissionsAtom);
	const [{ data: members, loading: membersLoading }] = useAtom(membersAtom);

	const [selectedEmployee, setSelectedEmployee] = useAtom<string>(selectedEmployeeAtom);
	const [selectedOrganization, setSelectedOrganization] = useAtom(selectedOrganizationAtom);
	const [selectedTeam, setSelectedTeam] = useAtom<string>(selectedTeamAtom);

	const [{ data: statisticsCounts, loading: statisticsCountsLoading }] = useAtom(statisticsCountsAtom);
	const [{ data: projectsStats, loading: projectsStatsLoading }] = useAtom(projectsStatsAtom);
	const [{ data: tasksStats, loading: tasksStatsLoading }] = useAtom(tasksStatsAtom);
	const [{ data: activitiesStats, loading: activitiesStatsLoading }] = useAtom(activitiesStatsAtom);
	const [{ data: userOrganizations, loading: userOrganizationsLoading }] = useAtom(userOrganizationsAtom);

	const context = useContext(ClocContext);

	if (!context) {
		throw new Error(
			'Remember to wrap your application or components in a `<ClocProvider> {...} </ClocProvider>` !!!'
		);
	}

	return {
		...context,
		fontOptions,
		selectedFont,
		setSelectedFont: setSelectedFont,
		// start,
		// pause,
		// startTimer,
		// stopTimer,
		// isRunning,
		// hours,
		// minutes,
		// seconds,
		// totalSeconds,
		// defaultData,
		// config: chartConfig,

		selectedEmployee,
		setSelectedEmployee,

		selectedTeam,
		setSelectedTeam,

		selectedOrganization,
		setSelectedOrganization,

		appliedTheme,
		setAppliedTheme,

		timerLoading,
		setTimerLoading,

		report,

		statisticsCounts,
		projectsStats,
		activitiesStats,
		tasksStats,

		authenticatedUser: user,
		setAuthenticatedUser: setUser,

		token: accessToken,
		setToken: setAccessToken,

		timerStatus: timerStatus,

		organizationClients,

		organizationTeams,
		tasks: userTasks,
		organizationProjects,
		reportDates,
		setReportDates,

		userPermissions,
		setPermissions,

		userOrganizations,

		// todayTrackedTime,
		currentClocState,
		setCurrentClocState,

		members,

		// handleChangeToken: {
		// 	handleTokenSubmit: handleTokenSubmit,
		// 	handleInputChange: handleInputChange
		// },
		loadings: {
			reportLoading,
			userLoading,
			userTasksLoading,
			organisationsLoading,
			projectsLoading,
			timerStatusLoading,
			clientsLoading,
			permissionLoading,
			statisticsCountsLoading,
			projectsStatsLoading,
			tasksStatsLoading,
			activitiesStatsLoading,
			membersLoading,
			userOrganizationsLoading
		}
	};
};

export { ClocContext, useClocContext };
