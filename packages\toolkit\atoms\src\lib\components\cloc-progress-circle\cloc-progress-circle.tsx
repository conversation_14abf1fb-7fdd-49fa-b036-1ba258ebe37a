/** @jsxImportSource theme-ui */

import { useClocContext } from '../../context/cloc-context';
import { getColorHex } from '../../themes/themes';
import { ProgressCircle, ProgressCircleProps } from './circle';

export function ClocProgressCircle({ ...props }: ProgressCircleProps) {
	const { totalSeconds, appliedTheme } = useClocContext();
	const dailyPickHours = 8 * 60 * 60;
	return (
		<ProgressCircle
			{...props}
			percentage={Math.floor((totalSeconds * 100) / dailyPickHours)}
			colors={{
				background: hexToRgba(getColorHex(appliedTheme.colors?.borderColor as string), 0.2),
				primary: appliedTheme.colors?.borderColor as string,
				secondary: appliedTheme.colors?.borderColor as string
			}}
		/>
	);
}

const hexToRgba = (hex: string, opacity: number): string => {
	const validHex = /^#([0-9A-F]{6})$/i.test(hex);
	if (!validHex) return `rgba(0, 0, 0, ${opacity})`;
	const r = parseInt(hex.slice(1, 3), 16);
	const g = parseInt(hex.slice(3, 5), 16);
	const b = parseInt(hex.slice(5, 7), 16);
	return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
