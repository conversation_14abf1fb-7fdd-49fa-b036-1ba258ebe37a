{"BUTTONS": {"PAY": "crwdns11018:0crwdne11018:0", "ADD_EXISTING_USER": "crwdns3148:0crwdne3148:0", "ADD_NEW": "crwdns3150:0crwdne3150:0", "ADD": "crwdns2:0crwdne2:0", "CREATE": "crwdns4:0crwdne4:0", "REGISTER": "crwdns2133:0crwdne2133:0", "LOGIN": "crwdns10815:0crwdne10815:0", "ADD_NOTE": "crwdns2331:0crwdne2331:0", "EDIT": "crwdns6:0crwdne6:0", "MANAGE": "crwdns2100:0crwdne2100:0", "DETAILS": "crwdns2135:0crwdne2135:0", "DUPLICATE": "crwdns2459:0crwdne2459:0", "DELETE": "crwdns8:0crwdne8:0", "REMOVE": "crwdns3152:0crwdne3152:0", "ADD_EXISTING": "crwdns3154:0crwdne3154:0", "OK": "crwdns10:0crwdne10:0", "YES": "crwdns8012:0crwdne8012:0", "NO": "crwdns8014:0crwdne8014:0", "SAVE": "crwdns12:0crwdne12:0", "CLEAR_ALL": "crwdns4184:0crwdne4184:0", "BACK": "crwdns14:0crwdne14:0", "SENT": "crwdns2137:0crwdne2137:0", "ACCEPTED": "crwdns2139:0crwdne2139:0", "MARK_AS_SENT": "crwdns7260:0crwdne7260:0", "MARK_AS_ACCEPTED": "crwdns7262:0crwdne7262:0", "CANCEL": "crwdns16:0crwdne16:0", "CLOSE": "crwdns18:0crwdne18:0", "INVITE": "crwdns2343:0crwdne2343:0", "SELECT_ALL": "crwdns2345:0crwdne2345:0", "COPY_LINK": "crwdns2347:0crwdne2347:0", "MANAGE_INTERVIEWS": "crwdns7610:0crwdne7610:0", "MANAGE_INVITES": "crwdns7612:0crwdne7612:0", "MANAGE_SPRINTS": "crwdns7614:0crwdne7614:0", "RESEND": "crwdns2351:0crwdne2351:0", "NEXT": "crwdns2935:0crwdne2935:0", "PREVIOUS": "crwdns2937:0crwdne2937:0", "INVITE_AGAIN": "crwdns2985:0crwdne2985:0", "REQUEST": "crwdns3104:0crwdne3104:0", "HISTORY": "crwdns3096:0crwdne3096:0", "SYNC": "crwdns3554:0crwdne3554:0", "UPDATE": "crwdns3480:0crwdne3480:0", "AUTO_SYNC": "crwdns3724:0crwdne3724:0", "VIEW": "crwdns3784:0crwdne3784:0", "SEND": "crwdns3882:0crwdne3882:0", "ARCHIVE": "crwdns3924:0crwdne3924:0", "HIRE": "crwdns4332:0crwdne4332:0", "MANAGE_CATEGORIES": "crwdns7706:0crwdne7706:0", "REJECT": "crwdns4334:0crwdne4334:0", "FIND_TIME": "crwdns4008:0crwdne4008:0", "DOWNLOAD": "crwdns4010:0crwdne4010:0", "ADD_KNOWLEDGE_BASE": "crwdns4178:0crwdne4178:0", "CHOOSE_ICON": "crwdns4386:0crwdne4386:0", "MAKE_PRIVATE": "crwdns4210:0crwdne4210:0", "MAKE_PUBLIC": "crwdns4212:0crwdne4212:0", "KNOWLEDGE_BASES": "crwdns4308:0crwdne4308:0", "SELECT": "crwdns4310:0crwdne4310:0", "EMAIL": "crwdns4366:0crwdne4366:0", "CONVERT_TO_INVOICE": "crwdns7218:0crwdne7218:0", "TO_INVOICE": "crwdns10998:0crwdne10998:0", "PUBLIC_APPOINTMENT_BOOK": "crwdns7266:0crwdne7266:0", "SAVE_AS_DRAFT": "crwdns7368:0crwdne7368:0", "SAVE_AND_SEND_CONTACT": "crwdns7622:0crwdne7622:0", "SAVE_AND_SEND_EMAIL": "crwdns7372:0crwdne7372:0", "EVENT_TYPES": "crwdns7480:0crwdne7480:0", "SEARCH": "crwdns7674:0crwdne7674:0", "RESET": "crwdns7676:0crwdne7676:0", "LEAVE_FEEDBACK": "crwdns8024:0crwdne8024:0", "SPRINT": {"CREATE": "crwdns7238:0crwdne7238:0", "EDIT": "crwdns7240:0crwdne7240:0", "DELETE": "crwdns7242:0crwdne7242:0"}, "CANDIDATE_STATISTIC": "crwdns7586:0crwdne7586:0", "FILTER": "crwdns8278:0crwdne8278:0", "REFRESH": "crwdns8280:0crwdne8280:0", "AUTO_REFRESH": "crwdns8282:0crwdne8282:0", "PROPOSAL_DELETE_MESSAGE": "crwdns8300:0crwdne8300:0", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "crwdns8302:0crwdne8302:0", "MANAGE_TEMPLATES": "crwdns8326:0crwdne8326:0", "MAKE_DEFAULT": "crwdns8304:0crwdne8304:0", "HIDE_ALL": "crwdns8352:0crwdne8352:0", "SCHEDULES": "crwdns8442:0crwdne8442:0", "YES_HIDE_ALL_JOBS": "crwdns8354:0crwdne8354:0", "VALIDATE": "crwdns8402:0crwdne8402:0", "VALIDATED": "crwdns8404:0crwdne8404:0", "APPROVE": "crwdns8642:0crwdne8642:0", "DENY": "crwdns8644:0crwdne8644:0", "PAYMENTS": "crwdns8646:0crwdne8646:0", "NOTE": "crwdns8706:0crwdne8706:0", "SKIP_CONTINUE": "crwdns8760:0{{ label }}crwdne8760:0", "BUY": "crwdns8762:0crwdne8762:0", "DELETE_ACCOUNT": "crwdns9186:0crwdne9186:0", "DELETE_ALL_DATA": "crwdns10316:0crwdne10316:0", "SELECT_AND_CONTINUE": "crwdns9188:0crwdne9188:0", "ADD_KPI": "crwdns9190:0crwdne9190:0", "PAID_DAYS_OFF": "crwdns9918:0crwdne9918:0", "UNPAID_DAYS_OFF": "crwdns9920:0crwdne9920:0", "CLEAR": "crwdns9922:0crwdne9922:0", "SET": "crwdns9924:0crwdne9924:0", "RECORD_FULL_PAYMENT": "crwdns10030:0crwdne10030:0", "EXPORT_TO_CSV": "crwdns10032:0crwdne10032:0", "INVOICE_REMAINING_AMOUNT": "crwdns10036:0crwdne10036:0", "PUBLIC_LINK": "crwdns10058:0crwdne10058:0", "GENERATE": "crwdns10060:0crwdne10060:0", "SEND_RECEIPT": "crwdns10122:0crwdne10122:0", "ADD_COMMENT": "crwdns10132:0crwdne10132:0", "CONTINUE": "crwdns10533:0crwdne10533:0", "SUPER_ADMIN_DEMO": "crwdns10817:0crwdne10817:0", "ADMIN_DEMO": "crwdns10819:0crwdne10819:0", "EMPLOYEE_DEMO": "crwdns10821:0crwdne10821:0", "DEMO_CREDENTIALS": "crwdns10823:0crwdne10823:0", "CREATE_NEW_ROLE": "crwdns10855:0{{ name }}crwdne10855:0", "DELETE_EXISTING_ROLE": "crwdns10857:0{{ name }}crwdne10857:0", "RESTORE": "crwdns10873:0crwdne10873:0", "VIEW_ALL": "crwdns10927:0crwdne10927:0", "VIEW_REPORT": "crwdns10931:0crwdne10931:0", "TIME_TRACKING_ENABLE": "crwdns10940:0crwdne10940:0", "TIME_TRACKING_DISABLE": "crwdns10942:0crwdne10942:0", "PRINT": "crwdns11070:0crwdne11070:0", "FEEDBACK": "crwdns11132:0crwdne11132:0", "EQUIPMENT_SHARING": "crwdns11146:0crwdne11146:0", "PRIVATE": "crwdns11217:0crwdne11217:0", "PUBLIC": "crwdns11219:0crwdne11219:0", "MANAGE_WIDGET": "crwdns11457:0crwdne11457:0", "MOVE": "crwdns11459:0crwdne11459:0", "COLLAPSE": "crwdns11461:0crwdne11461:0", "EXPAND": "crwdns11463:0crwdne11463:0", "SHOW_MORE": "crwdns11483:0crwdne11483:0", "START_WORK": "crwdns11525:0crwdne11525:0"}, "SM_TABLE": {"NO_DATA": {"RECEIVE_ESTIMATE": "crwdns11329:0crwdne11329:0", "INCOME": "crwdns11331:0crwdne11331:0", "EXPENSE_CATEGORY": "crwdns11333:0crwdne11333:0", "REPORT": "crwdns11335:0crwdne11335:0", "CONTRACT": "crwdns11337:0crwdne11337:0", "TEAM": "crwdns11339:0crwdne11339:0", "HISTORY_RECORD": "crwdns11341:0crwdne11341:0", "PROFIT_HISTORY": "crwdns11343:0crwdne11343:0", "EMPLOYEE": "crwdns11345:0crwdne11345:0", "EXPENSE": "crwdns11347:0crwdne11347:0", "PAYMENT": "crwdns11349:0crwdne11349:0", "PROPOSAL_TEMPLATE": "crwdns11351:0crwdne11351:0", "PROPOSAL": "crwdns11353:0crwdne11353:0", "PIPELINE": "crwdns11355:0crwdne11355:0", "TASK": "crwdns11357:0crwdne11357:0", "INVITE": "crwdns11359:0crwdne11359:0", "APPROVAL_REQUEST": "crwdns11361:0crwdne11361:0", "APPROVAL_POLICY": "crwdns11363:0crwdne11363:0", "TIME_OFF": "crwdns11365:0crwdne11365:0", "CANDIDATE": "crwdns11367:0crwdne11367:0", "INTERVIEW": "crwdns11369:0crwdne11369:0", "EQUIPMENT": "crwdns11371:0crwdne11371:0", "EQUIPMENT_SHARING": "crwdns11373:0crwdne11373:0", "EQUIPMENT_SHARING_POLICY": "crwdns11375:0crwdne11375:0", "INVENTORY": "crwdns11377:0crwdne11377:0", "MERCHANT": "crwdns11379:0crwdne11379:0", "WAREHOUSE": "crwdns11381:0crwdne11381:0", "WAREHOUSE_PRODUCT": "crwdns11479:0crwdne11479:0", "PRODUCT_CATEGORY": "crwdns11383:0crwdne11383:0", "TAGS": "crwdns11385:0crwdne11385:0", "PROJECT": "crwdns11465:0crwdne11465:0", "DEPARTMENT": "crwdns11389:0crwdne11389:0", "CONTACT": "crwdns11391:0crwdne11391:0", "CLIENT": "crwdns11411:0crwdne11411:0", "LEAD": "crwdns11419:0crwdne11419:0", "TIME_FRAME": "crwdns11393:0crwdne11393:0", "KPI": "crwdns11395:0crwdne11395:0", "INVOICE": "crwdns11397:0crwdne11397:0", "ESTIMATE": "crwdns11399:0crwdne11399:0", "EVENT_TYPE": "crwdns11401:0crwdne11401:0", "PRODUCT_TYPE_NO_DATA": "crwdns11481:0crwdne11481:0", "TEAM_DASHBOARD": "crwdns11521:0crwdne11521:0"}, "TRANSACTION_TYPE": "crwdns7534:0crwdne7534:0", "AMOUNT": "crwdns7536:0crwdne7536:0", "DATE": "crwdns20:0crwdne20:0", "TITLE": "crwdns7192:0crwdne7192:0", "STAGE": "crwdns7194:0crwdne7194:0", "START_DATE": "crwdns4322:0crwdne4322:0", "END_DATE": "crwdns4324:0crwdne4324:0", "CLIENT_NAME": "crwdns22:0crwdne22:0", "CONTACT_NAME": "crwdns5104:0crwdne5104:0", "NAME": "crwdns3556:0crwdne3556:0", "VENDOR": "crwdns24:0crwdne24:0", "CATEGORY": "crwdns26:0crwdne26:0", "CURRENCY": "crwdns28:0crwdne28:0", "VALUE": "crwdns30:0crwdne30:0", "NOTES": "crwdns32:0crwdne32:0", "EMPLOYEE": "crwdns34:0crwdne34:0", "EMPLOYEES": "crwdns36:0crwdne36:0", "FULL_NAME": "crwdns40:0crwdne40:0", "EMAIL": "crwdns42:0crwdne42:0", "INCOME": "crwdns44:0crwdne44:0", "EXPENSES": "crwdns46:0crwdne46:0", "BONUS": "crwdns48:0crwdne48:0", "BONUS_AVG": "crwdns50:0crwdne50:0", "PROFIT_BASED_BONUS": "crwdns2687:0crwdne2687:0", "REVENUE_BASED_BONUS": "crwdns2689:0crwdne2689:0", "STATUS": "crwdns52:0crwdne52:0", "SOURCE": "crwdns3326:0crwdne3326:0", "WORK_STATUS": "crwdns54:0crwdne54:0", "TODAY": "crwdns56:0crwdne56:0", "END_OF_MONTH": "crwdns58:0crwdne58:0", "START_OF_MONTH": "crwdns60:0crwdne60:0", "RATE": "crwdns62:0crwdne62:0", "FLAT_FEE": "crwdns64:0crwdne64:0", "MILESTONES": "crwdns66:0crwdne66:0", "JOB_TITLE": "crwdns2143:0crwdne2143:0", "JOB_POST_URL": "crwdns7264:0crwdne7264:0", "LINK_TO_JOBPOST": "crwdns2147:0crwdne2147:0", "AUTHOR": "crwdns2149:0crwdne2149:0", "MONDAY": "crwdns2167:0crwdne2167:0", "TUESDAY": "crwdns2169:0crwdne2169:0", "WEDNESDAY": "crwdns2171:0crwdne2171:0", "THURSDAY": "crwdns2173:0crwdne2173:0", "FRIDAY": "crwdns2175:0crwdne2175:0", "SATURDAY": "crwdns2177:0crwdne2177:0", "SUNDAY": "crwdns2179:0crwdne2179:0", "NONE": "crwdns2271:0crwdne2271:0", "ROLE": "crwdns2303:0crwdne2303:0", "PROJECTS": "crwdns2353:0crwdne2353:0", "PROJECT": "crwdns10262:0crwdne10262:0", "INVITED_BY": "crwdns2355:0crwdne2355:0", "EXPIRE_DATE": "crwdns2357:0crwdne2357:0", "CLIENTS": "crwdns2445:0crwdne2445:0", "CONTACTS": "crwdns7180:0crwdne7180:0", "CONTACT": "crwdns10589:0crwdne10589:0", "DEPARTMENTS": "crwdns2447:0crwdne2447:0", "DESCRIPTION": "crwdns2919:0crwdne2919:0", "POLICY": "crwdns7288:0crwdne7288:0", "APPLIED": "crwdns3926:0crwdne3926:0", "HIRED": "crwdns3928:0crwdne3928:0", "REJECTED": "crwdns3930:0crwdne3930:0", "NO_RESULT": "crwdns4728:0crwdne4728:0", "CLIENT": "crwdns6928:0crwdne6928:0", "INTERNAL": "crwdns6930:0crwdne6930:0", "START": "crwdns7290:0crwdne7290:0", "END": "crwdns7292:0crwdne7292:0", "REQUEST_DATE": "crwdns7294:0crwdne7294:0", "REGION": {"BG": "crwdns2483:0crwdne2483:0", "EN": "crwdns2485:0crwdne2485:0", "RU": "crwdns2487:0crwdne2487:0", "HE": "crwdns2489:0crwdne2489:0"}, "CURRENT_VALUE": "crwdns8768:0crwdne8768:0", "TARGET_VALUE": "crwdns8770:0crwdne8770:0", "LAST_UPDATED": "crwdns8772:0crwdne8772:0", "CREATED_BY": "crwdns9192:0crwdne9192:0", "NO_DATA_MESSAGE": "crwdns9194:0crwdne9194:0", "TAGS": "crwdns10402:0crwdne10402:0", "CREATED": "crwdns10559:0crwdne10559:0", "HIRED_DATE": "crwdns10877:0crwdne10877:0", "REJECTED_DATE": "crwdns10879:0crwdne10879:0", "TIME_TRACKING": "crwdns10934:0crwdne10934:0", "CREATED_AT": "crwdns11136:0crwdne11136:0"}, "FORM": {"USERNAME": "crwdns68:0crwdne68:0", "PASSWORD": "crwdns72:0crwdne72:0", "CONFIRM": "crwdns74:0crwdne74:0", "FILTER": "crwdns4730:0crwdne4730:0", "EMAIL": "crwdns70:0crwdne70:0", "LABELS": {"NAME": "crwdns76:0crwdne76:0", "WEBSITE": "crwdns7958:0crwdne7958:0", "FIRST_NAME": "crwdns78:0crwdne78:0", "LAST_NAME": "crwdns80:0crwdne80:0", "FROM": "crwdns7296:0crwdne7296:0", "TO": "crwdns7298:0crwdne7298:0", "EMPLOYEE": "crwdns7300:0crwdne7300:0", "START_DATE": "crwdns11503:0crwdne11503:0", "APPLIED_DATE_LABEL": "crwdns3440:0crwdne3440:0", "IMAGE_URL": "crwdns84:0crwdne84:0", "CV_URL": "crwdns3430:0crwdne3430:0", "DOCUMENT_URL": "crwdns3824:0crwdne3824:0", "CURRENCY": "crwdns86:0crwdne86:0", "DATE_TYPE": "crwdns88:0crwdne88:0", "ADD_TEAM": "crwdns2125:0crwdne2125:0", "EDIT_TEAM": "crwdns7744:0crwdne7744:0", "OFFICIAL_NAME": "crwdns2181:0crwdne2181:0", "PROFILE_LINK": "crwdns3568:0crwdne3568:0", "START_WEEK_ON": "crwdns2183:0crwdne2183:0", "TAX_ID": "crwdns2185:0crwdne2185:0", "TIME_FORMAT": "crwdns7994:0crwdne7994:0", "COUNTRY": "crwdns2187:0crwdne2187:0", "CITY": "crwdns2189:0crwdne2189:0", "ADDRESS": "crwdns2191:0crwdne2191:0", "ADDRESS_2": "crwdns2193:0crwdne2193:0", "LOGO_ALIGNMENT": "crwdns2209:0crwdne2209:0", "BRAND_COLOR": "crwdns2211:0crwdne2211:0", "DATE_FORMAT": "crwdns2213:0crwdne2213:0", "CHOOSE_TIME_ZONE": "crwdns2215:0crwdne2215:0", "START_TIME": "crwdns8444:0crwdne8444:0", "END_TIME": "crwdns8446:0crwdne8446:0", "POSTCODE": "crwdns2259:0crwdne2259:0", "PAY_PERIOD": "crwdns2281:0crwdne2281:0", "BILL_RATE": "crwdns2293:0crwdne2293:0", "CURRENCY_PER_HOUR": "crwdns2295:0crwdne2295:0", "RECURRING_WEEKLY_LIMIT": "crwdns2287:0crwdne2287:0", "ROLE": "crwdns2305:0crwdne2305:0", "SOURCE": "crwdns8110:0crwdne8110:0", "EMAILS": "crwdns2359:0crwdne2359:0", "PROJECTS_OPTIONAL": "crwdns2361:0crwdne2361:0", "CONTACTS_OPTIONAL": "crwdns7182:0crwdne7182:0", "DEPARTMENTS_OPTIONAL": "crwdns2451:0crwdne2451:0", "TEAMS_OPTIONAL": "crwdns11517:0crwdne11517:0", "PROJECTS": "crwdns2513:0crwdne2513:0", "ADD_NEW_DEPARTMENT": "crwdns2575:0crwdne2575:0", "EDIT_DEPARTMENT": "crwdns2577:0crwdne2577:0", "TYPE_OF_BONUS": "crwdns8500:0crwdne8500:0", "BONUS_PERCENTAGE": "crwdns2693:0crwdne2693:0", "ENABLE_DISABLE_INVITES": "crwdns2846:0crwdne2846:0", "ALLOW_USER_INVITES": "crwdns2848:0crwdne2848:0", "INVITE_EXPIRY_PERIOD": "crwdns2850:0crwdne2850:0", "EMPLOYMENT_TYPES": "crwdns3051:0crwdne3051:0", "ADD_NEW_EMPLOYMENT_TYPE": "crwdns3053:0crwdne3053:0", "OFFER_DATE": "crwdns2961:0crwdne2961:0", "ACCEPT_DATE": "crwdns2963:0crwdne2963:0", "APPLIED_DATE": "crwdns3432:0crwdne3432:0", "EDUCATION": "crwdns3492:0crwdne3492:0", "EXPERIENCE": "crwdns3494:0crwdne3494:0", "SKILLS": "crwdns3496:0crwdne3496:0", "HIRED_DATE": "crwdns3434:0crwdne3434:0", "REJECT_DATE": "crwdns2965:0crwdne2965:0", "DOCUMENT_NAME": "crwdns3826:0crwdne3826:0", "FEEDBACK_DESCRIPTION": "crwdns3866:0crwdne3866:0", "EMAIL_INVITATION": "crwdns2979:0crwdne2979:0", "SELECT_EQUIPMENT": "crwdns3106:0crwdne3106:0", "SELECT_SHARE_REQUEST_DATE": "crwdns3108:0crwdne3108:0", "SELECT_SHARE_START_DATE": "crwdns3110:0crwdne3110:0", "SELECT_SHARE_END_DATE": "crwdns3112:0crwdne3112:0", "SELECT_EMPLOYEE": "crwdns3114:0crwdne3114:0", "SELECT_TEAM": "crwdns3116:0crwdne3116:0", "ENABLE_DISABLE_FUTURE_DATE": "crwdns3073:0crwdne3073:0", "ALLOW_FUTURE_DATE": "crwdns3075:0crwdne3075:0", "REGISTRATION_DATE": "crwdns3093:0crwdne3093:0", "ORGANIZATION_NAME": "crwdns3472:0crwdne3472:0", "MEETING_AGENDA": "crwdns3942:0crwdne3942:0", "MEETING_LOCATION": "crwdns3944:0crwdne3944:0", "MEETING_DESCRIPTION": "crwdns3946:0crwdne3946:0", "MEETING_INVITEES": "crwdns3948:0crwdne3948:0", "TITLE": "crwdns3962:0crwdne3962:0", "DATE": "crwdns3964:0crwdne3964:0", "TIME": "crwdns3966:0crwdne3966:0", "DURATION": "crwdns3968:0crwdne3968:0", "CANDIDATE": "crwdns4116:0crwdne4116:0", "INTERVIEWERS": "crwdns3970:0crwdne3970:0", "LOCATION": "crwdns3972:0crwdne3972:0", "NOTE": "crwdns3974:0crwdne3974:0", "PREFERRED_LANGUAGE": "crwdns4064:0crwdne4064:0", "DESCRIPTION": "crwdns4086:0crwdne4086:0", "DESCRIPTION_OPTIONAL": "crwdns4968:0crwdne4968:0", "ADD_OR_REMOVE_EMPLOYEES": "crwdns4406:0crwdne4406:0", "ADD_REMOVE_MANAGERS": "crwdns4758:0crwdne4758:0", "ADD_REMOVE_MEMBERS": "crwdns4760:0crwdne4760:0", "SHORT_DESCRIPTION": "crwdns4970:0crwdne4970:0", "ENABLE_EMPLOYEE_FEATURES": "crwdns7256:0crwdne7256:0", "REVOKE_EMPLOYEE_FEATURES": "crwdns7258:0crwdne7258:0", "STATUS": "crwdns7448:0crwdne7448:0", "FISCAL_YEAR_START_DATE": "crwdns8448:0crwdne8448:0", "FISCAL_YEAR_END_DATE": "crwdns8450:0crwdne8450:0", "TAX_AND_DISCOUNT_INVOICE_ITEMS_SEPARATELY": "crwdns8452:0crwdne8452:0", "ALLOW_TAXING_AND_DISCOUNTING_OF_INVOICE_ITEMS_SEPARATELY": "crwdns8454:0crwdne8454:0", "DISCOUNT_AFTER_TAX": "crwdns8456:0crwdne8456:0", "APPLY_DISCOUNT_AFTER_TAX_FOR_INVOICES_AND_ESTIMATES": "crwdns8458:0crwdne8458:0", "FIND_ADDRESS": "crwdns8502:0crwdne8502:0", "LINKEDIN": "crwdns8612:0crwdne8612:0", "FACEBOOK": "crwdns8614:0crwdne8614:0", "INSTAGRAM": "crwdns8616:0crwdne8616:0", "TWITTER": "crwdns8618:0crwdne8618:0", "GITHUB": "crwdns8620:0crwdne8620:0", "GITLAB": "crwdns8622:0crwdne8622:0", "UPWORK": "crwdns8624:0crwdne8624:0", "STACK_OVERFLOW": "crwdns10555:0crwdne10555:0", "PROJECT_URL": "crwdns8648:0crwdne8648:0", "CLIENTS": "crwdns8650:0crwdne8650:0", "IS_PROJECT_OPEN_SOURCE": "crwdns8652:0crwdne8652:0", "OPEN_SOURCE_PROJECT_URL": "crwdns8654:0crwdne8654:0", "EMPLOYEE_LEVEL": "crwdns9196:0crwdne9196:0", "UNIT": "crwdns9198:0crwdne9198:0", "SELECT_EXISTING_OBJECTIVE": "crwdns9200:0crwdne9200:0", "LENGTH": "crwdns9926:0crwdne9926:0", "DATE_START": "crwdns9928:0crwdne9928:0", "END_DATE": "crwdns9930:0crwdne9930:0", "GOAL": "crwdns9932:0crwdne9932:0", "DOWNLOAD_REQUEST_FORM": "crwdns9934:0crwdne9934:0", "COORDINATE": {"TITLE": "crwdns8504:0crwdne8504:0", "LATITUDE": "crwdns8506:0crwdne8506:0", "LONGITUDE": "crwdns8508:0crwdne8508:0"}, "PUBLIC_LINK": "crwdns10062:0crwdne10062:0", "DEFAULT_TERMS": "crwdns10084:0crwdne10084:0", "CONVERT_ESTIMATES": "crwdns10092:0crwdne10092:0", "ALLOW_CONVERTING": "crwdns10094:0crwdne10094:0", "DEFAULT_DAYS": "crwdns10088:0crwdne10088:0", "TEMPLATE_NAME": "crwdns10226:0crwdne10226:0", "TEMPLATE_BODY": "crwdns10228:0crwdne10228:0", "TEMPLATE_PREVIEW": "crwdns10230:0crwdne10230:0", "LANGUAGE": "crwdns10232:0crwdne10232:0", "DEFAULT_INVOICE_TEMPLATE": "crwdns10376:0crwdne10376:0", "DEFAULT_ESTIMATE_TEMPLATE": "crwdns10378:0crwdne10378:0", "DEFAULT_RECEIPT_TEMPLATE": "crwdns10380:0crwdne10380:0", "DEFAULT": "crwdns10382:0crwdne10382:0", "INVITATION_EXPIRATION": "crwdns10561:0crwdne10561:0", "PERIOD": "crwdns11327:0crwdne11327:0", "REGISTER_AS_EMPLOYEE_OF_ORGANIZATION": "crwdns11505:0crwdne11505:0"}, "PLACEHOLDERS": {"NAME": "crwdns90:0crwdne90:0", "DEFAULT": "crwdns10384:0crwdne10384:0", "FIRST_NAME": "crwdns92:0crwdne92:0", "LAST_NAME": "crwdns94:0crwdne94:0", "COMPANY_NAME": "crwdns96:0crwdne96:0", "ALL_EMPLOYEES": "crwdns98:0crwdne98:0", "CURRENCY": "crwdns100:0crwdne100:0", "ALL_CURRENCIES": "crwdns102:0crwdne102:0", "ALL_DEPARTMENTS": "crwdns104:0crwdne104:0", "ALL_POSITIONS": "crwdns106:0crwdne106:0", "START_DATE": "crwdns108:0crwdne108:0", "PICK_DATE": "crwdns110:0crwdne110:0", "DATE_TYPE": "crwdns112:0crwdne112:0", "BILLING": "crwdns6932:0crwdne6932:0", "BILLABLE": "crwdns7960:0crwdne7960:0", "CODE": "crwdns7962:0crwdne7962:0", "COLOR": "crwdns7964:0crwdne7964:0", "WEBSITE": "crwdns7966:0crwdne7966:0", "CURRENCY_POSITION": "crwdns7968:0crwdne7968:0", "FISCAL_INFORMATION": "crwdns7970:0crwdne7970:0", "IMAGE_URL": "crwdns116:0crwdne116:0", "ADD_DEPARTMENT": "crwdns118:0crwdne118:0", "ADD_POSITION": "crwdns2461:0crwdne2461:0", "ADD_VENDOR": "crwdns122:0crwdne122:0", "ADD_SKILL": "crwdns3672:0crwdne3672:0", "ADD_CANDIDATE_QUALITY": "crwdns4574:0crwdne4574:0", "ADD_TECHNOLOGY": "crwdns4576:0crwdne4576:0", "ADD_EXPENSE_CATEGORY": "crwdns3308:0crwdne3308:0", "CONTACTS": "crwdns5106:0crwdne5106:0", "START_DATE_PROJECT": "crwdns8656:0crwdne8656:0", "END_DATE_PROJECT": "crwdns8658:0crwdne8658:0", "TEAM_NAME": "crwdns2127:0crwdne2127:0", "ADD_REMOVE_MEMBERS": "crwdns2129:0crwdne2129:0", "ADD_REMOVE_MANAGERS": "crwdns4762:0crwdne4762:0", "MEMBERS_COUNT": "crwdns2131:0crwdne2131:0", "OFFICIAL_NAME": "crwdns2195:0crwdne2195:0", "PROFILE_LINK": "crwdns3570:0crwdne3570:0", "START_WEEK_ON": "crwdns2197:0crwdne2197:0", "TAX_ID": "crwdns2199:0crwdne2199:0", "COUNTRY": "crwdns2201:0crwdne2201:0", "CITY": "crwdns2203:0crwdne2203:0", "ADDRESS": "crwdns2205:0crwdne2205:0", "ADDRESS_2": "crwdns2207:0crwdne2207:0", "EDUCATION": "crwdns3498:0crwdne3498:0", "EXPERIENCE": "crwdns3500:0crwdne3500:0", "SKILLS": "crwdns3502:0crwdne3502:0", "POSTCODE": "crwdns2261:0crwdne2261:0", "BILL_RATE": "crwdns2297:0crwdne2297:0", "RECURRING_WEEKLY_LIMIT": "crwdns2291:0crwdne2291:0", "EMAILS": "crwdns2363:0crwdne2363:0", "ROLE": "crwdns2307:0crwdne2307:0", "PROJECTS": "crwdns2365:0crwdne2365:0", "CHOOSE_FORMAT": "crwdns2399:0crwdne2399:0", "CHOOSE_TIME_ZONE": "crwdns2401:0crwdne2401:0", "START_TIME": "crwdns8460:0crwdne8460:0", "END_TIME": "crwdns8462:0crwdne8462:0", "ADD_COLOR": "crwdns2403:0crwdne2403:0", "ALIGN_LOGO_TO": "crwdns2405:0crwdne2405:0", "DEPARTMENTS": "crwdns2453:0crwdne2453:0", "TEAMS": "crwdns11519:0crwdne11519:0", "NUMBER_FORMAT": "crwdns2491:0crwdne2491:0", "REGIONS": "crwdns2493:0crwdne2493:0", "REMOVE_IMAGE": "crwdns2535:0crwdne2535:0", "UPLOADER_PLACEHOLDER": "crwdns2537:0crwdne2537:0", "UPLOADER_DOCUMENT_PLACEHOLDER": "crwdns3828:0crwdne3828:0", "ADD_REMOVE_EMPLOYEES": "crwdns2579:0crwdne2579:0", "ADD_REMOVE_CANDIDATE": "crwdns4118:0crwdne4118:0", "ADD_REMOVE_EMPLOYEE": "crwdns4368:0crwdne4368:0", "ADD_REMOVE_INTERVIEWER": "crwdns7022:0crwdne7022:0", "ADD_REMOVE_CANDIDATES": "crwdns4302:0crwdne4302:0", "ADD_REMOVE_USERS": "crwdns3156:0crwdne3156:0", "ADD_REMOVE_ORGANIZATIONS": "crwdns3318:0crwdne3318:0", "ADD_ORGANIZATIONS": "crwdns3388:0crwdne3388:0", "DATE": "crwdns2627:0crwdne2627:0", "VALUE": "crwdns2629:0crwdne2629:0", "SELECT_CURRENCY": "crwdns2631:0crwdne2631:0", "TYPE_OF_BONUS": "crwdns2895:0crwdne2895:0", "BONUS_PERCENTAGE": "crwdns2697:0crwdne2697:0", "ENABLE_INVITES": "crwdns2852:0crwdne2852:0", "INVITE_EXPIRY_PERIOD": "crwdns2854:0crwdne2854:0", "SWITCH_PROJECT_STATE": "crwdns2923:0crwdne2923:0", "CHOOSE_EMPLOYEES": "crwdns3118:0crwdne3118:0", "CHOOSE_TEAMS": "crwdns3120:0crwdne3120:0", "CHOOSE_APPROVAL_POLICY": "crwdns7468:0crwdne7468:0", "EMPLOYMENT_TYPES": "crwdns3055:0crwdne3055:0", "REGISTRATION_DATE": "crwdns3095:0crwdne3095:0", "ORGANIZATIONS": "crwdns3320:0crwdne3320:0", "ORGANIZATION": "crwdns4732:0crwdne4732:0", "DOCUMENT_NAME": "crwdns3830:0crwdne3830:0", "FEEDBACK_DESCRIPTION": "crwdns3868:0crwdne3868:0", "PREFERRED_LANGUAGE": "crwdns4066:0crwdne4066:0", "OWNER": "crwdns6934:0crwdne6934:0", "TASK_VIEW_MODE": "crwdns7616:0crwdne7616:0", "SELECT_STATUS": "crwdns7450:0crwdne7450:0", "LINKEDIN": "crwdns8626:0crwdne8626:0", "FACEBOOK": "crwdns8628:0crwdne8628:0", "INSTAGRAM": "crwdns8630:0crwdne8630:0", "TWITTER": "crwdns8632:0crwdne8632:0", "GITHUB": "crwdns8634:0crwdne8634:0", "GITLAB": "crwdns8636:0crwdne8636:0", "UPWORK": "crwdns8638:0crwdne8638:0", "STACK_OVERFLOW": "crwdns10557:0crwdne10557:0", "STATUS": "crwdns8660:0crwdne8660:0", "INVOICE_NUMBER": "crwdns8662:0crwdne8662:0", "PROJECT_URL": "crwdns8664:0crwdne8664:0", "CLIENTS": "crwdns8666:0crwdne8666:0", "BUDGET_TYPE": "crwdns9018:0crwdne9018:0", "BUDGET": "crwdns9020:0crwdne9020:0", "HOURS": "crwdns9022:0crwdne9022:0", "COST": "crwdns9024:0crwdne9024:0", "ADD_EDUCATION": {"SCHOOL_NAME": "crwdns3674:0crwdne3674:0", "DEGREE": "crwdns3676:0crwdne3676:0", "FIELD_OF_STUDY": "crwdns3678:0crwdne3678:0", "DATE_OF_COMPLETION": "crwdns3680:0crwdne3680:0", "ADDITIONAL_NOTES": "crwdns3682:0crwdne3682:0"}, "ADD_EXPERIENCE": {"OCCUPATION": "crwdns3684:0crwdne3684:0", "ORGANIZATION": "crwdns3686:0crwdne3686:0", "DURATION": "crwdns3688:0crwdne3688:0", "DESCRIPTION": "crwdns3690:0crwdne3690:0"}, "ADD_INTERVIEW": {"TITLE": "crwdns3978:0crwdne3978:0", "DATE": "crwdns3980:0crwdne3980:0", "TIME": "crwdns3982:0crwdne3982:0", "DURATION": "crwdns3984:0crwdne3984:0", "INTERVIEWERS": "crwdns3986:0crwdne3986:0", "LOCATION": "crwdns3988:0crwdne3988:0", "NOTE": "crwdns3990:0crwdne3990:0", "TYPE": "crwdns3992:0crwdne3992:0", "CALL": "crwdns4336:0crwdne4336:0", "MEETING": "crwdns4338:0crwdne4338:0"}, "MEETING_AGENDA": "crwdns3950:0crwdne3950:0", "MEETING_LOCATION": "crwdns3952:0crwdne3952:0", "MEETING_DESCRIPTION": "crwdns3954:0crwdne3954:0", "BUFFER_TIME": "crwdns4052:0crwdne4052:0", "BREAK_TIME": "crwdns4054:0crwdne4054:0", "DESCRIPTION": "crwdns4088:0crwdne4088:0", "DURATION": "crwdns4090:0crwdne4090:0", "TITLE": "crwdns4092:0crwdne4092:0", "SHORT_DESCRIPTION": "crwdns4972:0crwdne4972:0", "EG_FULL_STACK_WEB_DEVELOPER": "crwdns4974:0crwdne4974:0", "COORDINATE": {"LATITUDE": "crwdns8510:0crwdne8510:0", "LONGITUDE": "crwdns8512:0crwdne8512:0"}, "SELECT_EXPENSE": "crwdns9202:0crwdne9202:0", "ADD_TITLE": "crwdns9204:0crwdne9204:0", "ALL_PROJECTS": "crwdns9206:0crwdne9206:0", "UPWORK_API_KEY": "crwdns9208:0crwdne9208:0", "UPWORK_SECRET": "crwdns9210:0crwdne9210:0", "SELECT_COMPANY": "crwdns9212:0crwdne9212:0", "TYPE_SEARCH_REQUEST": "crwdns9214:0crwdne9214:0", "SELECT_ICON": "crwdns9936:0crwdne9936:0", "SELECT": "crwdns9938:0crwdne9938:0", "SPRINT_LENGTH": "crwdns9940:0crwdne9940:0", "SPRINT_START_DATE": "crwdns9942:0crwdne9942:0", "SPRINT_END_DATE": "crwdns9944:0crwdne9944:0", "SPRINT_GOAL": "crwdns9946:0crwdne9946:0", "POLICY_NAME": "crwdns9948:0crwdne9948:0", "SELECT_DATE": "crwdns9950:0crwdne9950:0", "DAYS_UNTIL_DUE": "crwdns10090:0crwdne10090:0", "TEMPLATES": "crwdns10234:0crwdne10234:0", "INVOICE_TEMPLATE": "crwdns10244:0crwdne10244:0", "ESTIMATE_TEMPLATE": "crwdns10246:0crwdne10246:0", "RECEIPT_TEMPLATE": "crwdns10248:0crwdne10248:0", "INVITATION_EXPIRATION": "crwdns10563:0crwdne10563:0", "ADD_PROJECT": "crwdns11148:0crwdne11148:0", "ADD_EMPLOYEE": "crwdns11150:0crwdne11150:0", "ADD_ORGANIZATION": "crwdns11152:0crwdne11152:0"}, "RATES": {"DEFAULT_RATE": "crwdns2407:0crwdne2407:0", "EXPECTED_RATE": "crwdns3544:0crwdne3544:0", "LIMITS": "crwdns2409:0crwdne2409:0"}, "CHECKBOXES": {"INCLUDE_DELETED": "crwdns2299:0crwdne2299:0", "INCLUDE_ARCHIVED": "crwdns3932:0crwdne3932:0", "ONLY_PAST": "crwdns8040:0crwdne8040:0", "ONLY_FUTURE": "crwdns8042:0crwdne8042:0"}, "NOTIFICATIONS": {"STARTED_WORK_ON": "crwdns3268:0crwdne3268:0"}, "ARCHIVE_CONFIRMATION": {"SURE": "crwdns3934:0crwdne3934:0", "RECORD": "crwdns3936:0crwdne3936:0", "CANDIDATE": "crwdns3938:0crwdne3938:0"}, "CANDIDATE_ACTION_CONFIRMATION": {"SURE": "crwdns4340:0crwdne4340:0", "RECORD": "crwdns4342:0crwdne4342:0", "CANDIDATE": "crwdns4344:0crwdne4344:0", "HIRE": "crwdns4346:0crwdne4346:0", "REJECT": "crwdns4348:0crwdne4348:0"}, "DELETE_CONFIRMATION": {"REMOVE_ALL_DATA": "crwdns10318:0crwdne10318:0", "DELETE_ACCOUNT": "crwdns2767:0crwdne2767:0", "REMOVE_USER": "crwdns3786:0crwdne3786:0", "SURE": "crwdns130:0crwdne130:0", "RECORD": "crwdns132:0crwdne132:0", "USER_RECORD": "crwdns3250:0crwdne3250:0", "EMPLOYEE": "crwdns134:0crwdne134:0", "CANDIDATE": "crwdns3504:0crwdne3504:0", "EXPENSE": "crwdns136:0crwdne136:0", "USER": "crwdns2309:0crwdne2309:0", "INVITATION": "crwdns2455:0crwdne2455:0", "DELETE_USER": "crwdns3788:0crwdne3788:0", "EVENT_TYPE": "crwdns4168:0crwdne4168:0"}, "COUNTDOWN_CONFIRMATION": {"WAS": "crwdns10535:0crwdne10535:0", "ENABLED": "crwdns10537:0crwdne10537:0", "DISABLED": "crwdns10539:0crwdne10539:0", "WAIT_UNTIL_RELOAD": "crwdns10541:0crwdne10541:0"}}, "POP_UPS": {"SELECT_ORGANIZATION": "crwdns8332:0crwdne8332:0", "ADD_INCOME": "crwdns138:0crwdne138:0", "ADD_EXPENSE": "crwdns140:0crwdne140:0", "EMPLOYEE": "crwdns7984:0crwdne7984:0", "EDIT_INCOME": "crwdns142:0crwdne142:0", "EDIT_EXPENSE": "crwdns144:0crwdne144:0", "EDIT_PAGE": "crwdns3846:0crwdne3846:0", "SHORT_DESCRIPTION": "crwdns3848:0crwdne3848:0", "OVERVIEW": "crwdns3850:0crwdne3850:0", "COMPANY_NAME": "crwdns3852:0crwdne3852:0", "NAME": "crwdns4618:0crwdne4618:0", "YEAR": "crwdns4620:0crwdne4620:0", "BANNER": "crwdns3854:0crwdne3854:0", "SIZE": "crwdns4622:0crwdne4622:0", "YEAR_FOUNDED": "crwdns3856:0crwdne3856:0", "COMPANY_SIZE": "crwdns4624:0crwdne4624:0", "CLIENT_FOCUS": "crwdns4626:0crwdne4626:0", "DUPLICATE": "crwdns2463:0crwdne2463:0", "DATE": "crwdns146:0crwdne146:0", "PICK_DATE": "crwdns148:0crwdne148:0", "ALL_CONTACTS": "crwdns7624:0crwdne7624:0", "CONTACT": "crwdns7930:0crwdne7930:0", "ALL_VENDORS": "crwdns2333:0crwdne2333:0", "ALL_CATEGORIES": "crwdns154:0crwdne154:0", "CATEGORY_NAME": "crwdns156:0crwdne156:0", "EXPENSE_VALUE": "crwdns158:0crwdne158:0", "TAX_LABEL": "crwdns2337:0crwdne2337:0", "TAX_TYPE": "crwdns2339:0crwdne2339:0", "TAX_RATE": "crwdns2341:0crwdne2341:0", "RECURRING_EXPENSES": "crwdns160:0crwdne160:0", "PURPOSE": "crwdns2335:0crwdne2335:0", "BACK_TO_WORK": "crwdns162:0crwdne162:0", "END_WORK": "crwdns164:0crwdne164:0", "START_WORK_FOR": "crwdns11527:0crwdne11527:0", "AMOUNT": "crwdns166:0crwdne166:0", "NOTES": "crwdns168:0crwdne168:0", "EDIT": "crwdns170:0crwdne170:0", "ADD": "crwdns172:0crwdne172:0", "DELETE_RECURRING_EXPENSE": "crwdns2469:0crwdne2469:0", "DELETE_ONLY_THIS": "crwdns2471:0crwdne2471:0", "DELETE_THIS_FUTURE": "crwdns2473:0crwdne2473:0", "DELETE_ALL_ENTRIES": "crwdns2475:0crwdne2475:0", "CONFIRM": "crwdns2539:0crwdne2539:0", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "crwdns2541:0crwdne2541:0", "OK": "crwdns2543:0crwdne2543:0", "CANCEL": "crwdns2545:0crwdne2545:0", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "crwdns2547:0crwdne2547:0", "RECURRING_EXPENSE": "crwdns2875:0crwdne2875:0", "SPLIT_EXPENSE_WITH_INFO": "crwdns2897:0{{ originalValue }}crwdnd2897:0{{ employeeCount }}crwdne2897:0", "STARTS_ON": "crwdns3098:0crwdne3098:0", "EXPENSE_HISTORY": "crwdns3100:0crwdne3100:0", "NEW_EXPENSE_VALUE": "crwdns3102:0crwdne3102:0", "OFFICIAL_NAME": "crwdns3474:0crwdne3474:0", "ADD_EVENT_TYPE": "crwdns4094:0crwdne4094:0", "EDIT_EVENT_TYPE": "crwdns8774:0crwdne8774:0", "AWARDS": "crwdns4628:0crwdne4628:0", "LEVEL": "crwdns4630:0crwdne4630:0", "LANGUAGES": "crwdns4632:0crwdne4632:0", "TOTAL_INCOME_OR_MONTHLY_INCOME": "crwdns4634:0crwdne4634:0", "PROFITS": "crwdns4636:0crwdne4636:0", "BONUSES_PAID": "crwdns4638:0crwdne4638:0", "TOTAL_HOURS_WORKED_OVER_GAUZY": "crwdns4790:0crwdne4790:0", "MINIMUM_PROJECT_SIZE": "crwdns4642:0crwdne4642:0", "PROJECTS_COUNT": "crwdns4644:0crwdne4644:0", "CLIENTS_COUNT": "crwdns4824:0crwdne4824:0", "EMPLOYEES_COUNT": "crwdns7780:0crwdne7780:0", "DETAILS": "crwdns4792:0crwdne4792:0", "SKILLS": "crwdns4794:0crwdne4794:0", "PRIVACY": "crwdns4796:0crwdne4796:0", "SELECT_TIMEZONE": "crwdns7392:0crwdne7392:0", "SHOW_AVERAGE_BONUS": "crwdns7872:0crwdne7872:0", "SHOW_AVERAGE_INCOME": "crwdns7874:0crwdne7874:0", "SHOW_PAYPERIOD": "crwdns7876:0crwdne7876:0", "SHOW_ANONYMOUS_BONUS": "crwdns7878:0crwdne7878:0", "SHOW_AVERAGE_EXPENSES": "crwdns7880:0crwdne7880:0", "SHOW_BILLRATE": "crwdns7882:0crwdne7882:0", "SHOW_START_WORK_ON": "crwdns7884:0crwdne7884:0", "SHOW_CLIENTS": "crwdns7948:0crwdne7948:0", "DISPLAY_BONUS_ANONYMOUSLY": "crwdns7990:0crwdne7990:0", "SOURCE": "crwdns8112:0crwdne8112:0", "DESCRIPTION": "crwdns10551:0crwdne10551:0", "MAIN": "crwdns10553:0crwdne10553:0", "CATEGORIES": "crwdns10583:0crwdne10583:0", "ADD_LANGUAGE": "crwdns11209:0crwdne11209:0", "REGISTER_AS_EMPLOYEE_TOOLTIP": "crwdns11507:0crwdne11507:0"}, "MENU": {"PIPELINES": "crwdns4734:0crwdne4734:0", "DASHBOARD": "crwdns174:0crwdne174:0", "DASHBOARDS": "crwdns11523:0crwdne11523:0", "APPOINTMENTS": "crwdns3956:0crwdne3956:0", "ACCOUNTING": "crwdns3390:0crwdne3390:0", "INCOME": "crwdns176:0crwdne176:0", "EXPENSES": "crwdns178:0crwdne178:0", "RECURRING_EXPENSE": "crwdns7986:0crwdne7986:0", "POSITIONS": "crwdns7712:0crwdne7712:0", "INTEGRATIONS": "crwdns8128:0crwdne8128:0", "UPWORK": "crwdns3200:0crwdne3200:0", "PROPOSALS": "crwdns2104:0crwdne2104:0", "TIME_OFF": "crwdns2495:0crwdne2495:0", "APPROVALS": "crwdns4390:0crwdne4390:0", "HELP": "crwdns180:0crwdne180:0", "ABOUT": "crwdns182:0crwdne182:0", "CONTACTS": "crwdns7858:0crwdne7858:0", "ADMIN": "crwdns184:0crwdne184:0", "EMPLOYEE_LEVEL": "crwdns7714:0crwdne7714:0", "EMPLOYEES": "crwdns186:0crwdne186:0", "MANAGE": "crwdns3392:0crwdne3392:0", "CANDIDATES": "crwdns3328:0crwdne3328:0", "ORGANIZATIONS": "crwdns188:0crwdne188:0", "SETTINGS": "crwdns190:0crwdne190:0", "GENERAL": "crwdns192:0crwdne192:0", "EMAIL_HISTORY": "crwdns4388:0crwdne4388:0", "USERS": "crwdns2311:0crwdne2311:0", "ROLES": "crwdns2645:0crwdne2645:0", "DANGER_ZONE": "crwdns2769:0crwdne2769:0", "FILE_STORAGE": "crwdns8148:0crwdne8148:0", "INVITE_PEOPLE": "crwdns11064:0crwdne11064:0", "IMPORT_EXPORT": {"IMPORT_EXPORT": "crwdns2941:0crwdne2941:0", "IMPORT_EXPORT_DATA": "crwdns8588:0crwdne8588:0", "IMPORT": "crwdns2943:0crwdne2943:0", "IMPORT_HISTORY": "crwdns7940:0crwdne7940:0", "IMPORT_DATE_TIME": "crwdns10504:0crwdne10504:0", "EXPORT": "crwdns2945:0crwdne2945:0", "MERGE": "crwdns10506:0crwdne10506:0", "CLEAN_UP": "crwdns10508:0crwdne10508:0", "EXPORT_MESSAGE": "crwdns8590:0crwdne8590:0", "IMPORT_MESSAGE": "crwdns11213:0crwdne11213:0", "IMPORT_HISTORY_MESSAGE": "crwdns7944:0crwdne7944:0", "SELECT_FILE": "crwdns3798:0crwdne3798:0", "DROP_FILE": "crwdns3800:0crwdne3800:0", "NO_DROP_FILE": "crwdns11195:0crwdne11195:0", "BROWSE": "crwdns3802:0crwdne3802:0", "NAME": "crwdns3804:0crwdne3804:0", "SIZE": "crwdns3806:0crwdne3806:0", "PROGRESS": "crwdns3808:0crwdne3808:0", "STATUS": "crwdns3810:0crwdne3810:0", "ACTIONS": "crwdns3812:0crwdne3812:0", "QUEUE_PROGRESS": "crwdns3814:0crwdne3814:0", "CANCEL": "crwdns3816:0crwdne3816:0", "REMOVE": "crwdns3818:0crwdne3818:0", "WRONG_FILE_NAME": "crwdns3820:0crwdne3820:0", "CORRECT_FILE_NAME": "crwdns10512:0crwdne10512:0", "DOWNLOAD_TEMPLATES": "crwdns4256:0crwdne4256:0", "MIGRATE_TO_CLOUD": "crwdns10404:0crwdne10404:0", "IMPORT_INFO": "crwdns8592:0crwdne8592:0", "EXPORT_INFO": "crwdns8594:0crwdne8594:0", "DOWNLOAD_TEMPLATES_INFO": "crwdns8596:0crwdne8596:0", "MIGRATE_TO_CLOUD_INFO": "crwdns10406:0crwdne10406:0", "MIGRATE_SUCCESSFULLY": "crwdns10408:0{{ tenant }}crwdne10408:0", "ACCOUNTING_TEMPLATE": "crwdns10410:0crwdne10410:0", "ACTIVITY": "crwdns9216:0crwdne9216:0", "APPROVAL_POLICY": "crwdns9218:0crwdne9218:0", "AVAILABILITY_SLOT": "crwdns9220:0crwdne9220:0", "CANDIDATE": "crwdns9222:0crwdne9222:0", "CONTACT": "crwdns9224:0crwdne9224:0", "COUNTRY": "crwdns9226:0crwdne9226:0", "CURRENCY": "crwdns9228:0crwdne9228:0", "DEAL": "crwdns9230:0crwdne9230:0", "EMAIL": "crwdns9232:0crwdne9232:0", "EMPLOYEE": "crwdns9234:0crwdne9234:0", "EQUIPMENT": "crwdns9236:0crwdne9236:0", "EVENT_TYPES": "crwdns9238:0crwdne9238:0", "EXPENSE": "crwdns9240:0crwdne9240:0", "GOAL": "crwdns9242:0crwdne9242:0", "INCOME": "crwdns9244:0crwdne9244:0", "INTEGRATION": "crwdns9246:0crwdne9246:0", "INVITE": "crwdns9248:0crwdne9248:0", "INVOICE": "crwdns9250:0crwdne9250:0", "JOB": "crwdns9252:0crwdne9252:0", "KEY_RESULT": "crwdns9254:0crwdne9254:0", "KNOWLEDGE_BASE": "crwdns10412:0crwdne10412:0", "LANGUAGE": "crwdns9258:0crwdne9258:0", "MERCHANT": "crwdns10414:0crwdne10414:0", "ORGANIZATION": "crwdns9260:0crwdne9260:0", "PAYMENT": "crwdns9262:0crwdne9262:0", "PIPELINE": "crwdns9264:0crwdne9264:0", "PIPELINE_STAGE": "crwdns10416:0crwdne10416:0", "PRODUCT": "crwdns9266:0crwdne9266:0", "PROPOSAL": "crwdns9268:0crwdne9268:0", "REPORT": "crwdns9270:0crwdne9270:0", "REQUEST_APPROVAL": "crwdns9272:0crwdne9272:0", "ROLE": "crwdns9274:0crwdne9274:0", "SKILL": "crwdns9276:0crwdne9276:0", "TAG": "crwdns9280:0crwdne9280:0", "TASK": "crwdns9282:0crwdne9282:0", "TENANT": "crwdns9284:0crwdne9284:0", "TENANT_SETTING": "crwdns10514:0crwdne10514:0", "TIME_OFF_POLICY": "crwdns9286:0crwdne9286:0", "TIME_SHEET": "crwdns9288:0crwdne9288:0", "USER": "crwdns9290:0crwdne9290:0", "CANDIDATE_CRITERION_RATING": "crwdns10418:0crwdne10418:0", "CANDIDATE_DOCUMENT": "crwdns9294:0crwdne9294:0", "CANDIDATE_EDUCATION": "crwdns9296:0crwdne9296:0", "CANDIDATE_EXPERIENCE": "crwdns9298:0crwdne9298:0", "CANDIDATE_FEEDBACK": "crwdns9300:0crwdne9300:0", "CANDIDATE_INTERVIEW": "crwdns9302:0crwdne9302:0", "CANDIDATE_INTERVIEWER": "crwdns9304:0crwdne9304:0", "CANDIDATE_PERSONAL_QUALITY": "crwdns9306:0crwdne9306:0", "CANDIDATE_SKILL": "crwdns9308:0crwdne9308:0", "CANDIDATE_SOURCE": "crwdns9310:0crwdne9310:0", "CANDIDATE_TECHNOLOGY": "crwdns9312:0crwdne9312:0", "ORGANIZATION_AWARD": "crwdns10420:0crwdne10420:0", "ORGANIZATION_CONTACT": "crwdns9316:0crwdne9316:0", "ORGANIZATION_DEPARTMENT": "crwdns9318:0crwdne9318:0", "ORGANIZATION_DOCUMENT": "crwdns9320:0crwdne9320:0", "ORGANIZATION_EMPLOYEE_LEVEL": "crwdns9322:0crwdne9322:0", "ORGANIZATION_EMPLOYMENT_TYPE": "crwdns9324:0crwdne9324:0", "ORGANIZATION_LANGUAGES": "crwdns9326:0crwdne9326:0", "ORGANIZATION_POSITION": "crwdns9328:0crwdne9328:0", "ORGANIZATION_PROJECT": "crwdns9330:0crwdne9330:0", "ORGANIZATION_RECURRING_EXPENSE": "crwdns9332:0crwdne9332:0", "ORGANIZATION_SPRINT": "crwdns9334:0crwdne9334:0", "ORGANIZATION_TEAM": "crwdns9336:0crwdne9336:0", "ORGANIZATION_TEAM_EMPLOYEE": "crwdns9338:0crwdne9338:0", "ORGANIZATION_VENDOR": "crwdns9340:0crwdne9340:0", "EMAIL_TEMPLATE": "crwdns9342:0crwdne9342:0", "ESTIMATE_EMAIL": "crwdns9344:0crwdne9344:0", "EMPLOYEE_APPOINTMENT": "crwdns9346:0crwdne9346:0", "EMPLOYEE_AWARD": "crwdns9348:0crwdne9348:0", "EMPLOYEE_PROPOSAL_TEMPLATE": "crwdns9350:0crwdne9350:0", "EMPLOYEE_RECURRING_EXPENSE": "crwdns9352:0crwdne9352:0", "EMPLOYEE_SETTING": "crwdns9354:0crwdne9354:0", "EMPLOYEE_UPWORK_JOB_SEARCH_CRITERION": "crwdns10422:0crwdne10422:0", "INTEGRATION_ENTITY_SETTING": "crwdns9356:0crwdne9356:0", "INTEGRATION_ENTITY_SETTING_TIED_ENTITY": "crwdns9358:0crwdne9358:0", "INTEGRATION_MAP": "crwdns9360:0crwdne9360:0", "INTEGRATION_SETTING": "crwdns9362:0crwdne9362:0", "INTEGRATION_TENANT": "crwdns9364:0crwdne9364:0", "INTEGRATION_TYPE": "crwdns10516:0crwdne10516:0", "INVITE_ORGANIZATION_CONTACT": "crwdns9366:0crwdne9366:0", "INVITE_ORGANIZATION_DEPARTMENT": "crwdns9368:0crwdne9368:0", "INVITE_ORGANIZATION_PROJECT": "crwdns9370:0crwdne9370:0", "PRODUCT_CATEGORY": "crwdns9372:0crwdne9372:0", "PRODUCT_CATEGORY_TRANSLATION": "crwdns10424:0crwdne10424:0", "PRODUCT_GALLERY_ITEM": "crwdns10426:0crwdne10426:0", "PRODUCT_OPTION": "crwdns9374:0crwdne9374:0", "PRODUCT_OPTION_GROUP": "crwdns10428:0crwdne10428:0", "PRODUCT_OPTION_GROUP_TRANSLATION": "crwdns10430:0crwdne10430:0", "PRODUCT_OPTION_TRANSLATION": "crwdns10432:0crwdne10432:0", "PRODUCT_STORE": "crwdns10434:0crwdne10434:0", "PRODUCT_TRANSLATION": "crwdns10436:0crwdne10436:0", "PRODUCT_TYPE": "crwdns9376:0crwdne9376:0", "PRODUCT_TYPE_TRANSLATION": "crwdns10438:0crwdne10438:0", "PRODUCT_VARIANT": "crwdns9378:0crwdne9378:0", "PRODUCT_VARIANT_PRICE": "crwdns9380:0crwdne9380:0", "PRODUCT_VARIANT_SETTING": "crwdns9382:0crwdne9382:0", "REPORT_CATEGORY": "crwdns9384:0crwdne9384:0", "REPORT_ORGANIZATION": "crwdns10440:0crwdne10440:0", "REQUEST_APPROVAL_TAG": "crwdns9386:0crwdne9386:0", "REQUEST_APPROVAL_EMPLOYEE": "crwdns9388:0crwdne9388:0", "REQUEST_APPROVAL_TEAM": "crwdns9390:0crwdne9390:0", "SKILL_EMPLOYEE": "crwdns9392:0crwdne9392:0", "SKILL_ORGANIZATION": "crwdns9394:0crwdne9394:0", "TAG_CANDIDATE": "crwdns9396:0crwdne9396:0", "TAG_EMPLOYEE": "crwdns9398:0crwdne9398:0", "TAG_EQUIPMENT": "crwdns9400:0crwdne9400:0", "TAG_EVENT_TYPE": "crwdns9402:0crwdne9402:0", "TAG_EXPENSE": "crwdns9404:0crwdne9404:0", "TAG_INCOME": "crwdns10442:0crwdne10442:0", "TAG_INVOICE": "crwdns9408:0crwdne9408:0", "TAG_ORGANIZATION_CONTACT": "crwdns9410:0crwdne9410:0", "TAG_ORGANIZATION_DEPARTMENT": "crwdns9412:0crwdne9412:0", "TAG_ORGANIZATION_EMPLOYEE_LEVEL": "crwdns9414:0crwdne9414:0", "TAG_ORGANIZATION_EMPLOYEE_TYPE": "crwdns9416:0crwdne9416:0", "TAG_ORGANIZATION_EXPENSES_CATEGORY": "crwdns9418:0crwdne9418:0", "TAG_ORGANIZATION_POSITION": "crwdns9420:0crwdne9420:0", "TAG_ORGANIZATION_PROJECT": "crwdns9422:0crwdne9422:0", "TAG_ORGANIZATION_TEAM": "crwdns9424:0crwdne9424:0", "TAG_ORGANIZATION_VENDOR": "crwdns9426:0crwdne9426:0", "TAG_ORGANIZATIONS": "crwdns9428:0crwdne9428:0", "TAG_PAYMENT": "crwdns9430:0crwdne9430:0", "TAG_PRODUCT": "crwdns9432:0crwdne9432:0", "TAG_PROPOSAL": "crwdns9434:0crwdne9434:0", "TAG_TASK": "crwdns9436:0crwdne9436:0", "TAG_USER": "crwdns9438:0crwdne9438:0", "TASK_EMPLOYEE": "crwdns9440:0crwdne9440:0", "TASK_TEAM": "crwdns9442:0crwdne9442:0", "EQUIPMENT_SHARING": "crwdns9444:0crwdne9444:0", "EQUIPMENT_SHARE_POLICY": "crwdns10444:0crwdne10444:0", "EXPENSE_CATEGORY": "crwdns9446:0crwdne9446:0", "GOAL_KPI": "crwdns9448:0crwdne9448:0", "GOAL_GENERAL_SETTING": "crwdns10446:0crwdne10446:0", "GOAL_KPI_TEMPLATE": "crwdns9450:0crwdne9450:0", "GOAL_TEMPLATE": "crwdns9452:0crwdne9452:0", "GOAL_TIME_FRAME": "crwdns9454:0crwdne9454:0", "KNOWLEDGE_BASE_ARTICLE": "crwdns10448:0crwdne10448:0", "KNOWLEDGE_BASE_AUTHOR": "crwdns10450:0crwdne10450:0", "INVOICE_ESTIMATE_HISTORY": "crwdns9460:0crwdne9460:0", "INVOICE_ITEM": "crwdns9462:0crwdne9462:0", "JOB_PRESET": "crwdns9464:0crwdne9464:0", "JOB_PRESET_UPWORK_SEARCH_CRITERION": "crwdns10452:0crwdne10452:0", "JOB_SEARCH_OCCUPATION": "crwdns9466:0crwdne9466:0", "JOB_SEARCH_CATEGORY": "crwdns9468:0crwdne9468:0", "KEY_RESULT_TEMPLATE": "crwdns9470:0crwdne9470:0", "KEY_RESULT_UPDATE": "crwdns9472:0crwdne9472:0", "ROLE_PERMISSION": "crwdns10454:0crwdne10454:0", "TIME_OFF_POLICY_EMPLOYEE": "crwdns9476:0crwdne9476:0", "TIME_OFF_REQUEST": "crwdns9478:0crwdne9478:0", "TIME_OFF_REQUEST_EMPLOYEE": "crwdns9480:0crwdne9480:0", "SCREENSHOT": "crwdns9482:0crwdne9482:0", "TIME_LOG": "crwdns9484:0crwdne9484:0", "TIME_SLOT": "crwdns9486:0crwdne9486:0", "TIME_SLOT_MINUTES": "crwdns9488:0crwdne9488:0", "TIME_SLOT_TIME_LOGOS": "crwdns9490:0crwdne9490:0", "USER_ORGANIZATION": "crwdns10456:0crwdne10456:0", "WAREHOUSE": "crwdns10458:0crwdne10458:0", "WAREHOUSE_MERCHANT": "crwdns10460:0crwdne10460:0", "WAREHOUSE_PRODUCT": "crwdns10462:0crwdne10462:0", "WAREHOUSE_PRODUCT_VARIANT": "crwdns10464:0crwdne10464:0", "WAREHOUSE_STORE": "crwdns10466:0crwdne10466:0", "PRODUCT_IMAGE_ASSET": "crwdns10468:0crwdne10468:0", "CUSTOM_SMTP": "crwdns10470:0crwdne10470:0", "FEATURE": "crwdns10472:0crwdne10472:0", "FEATURE_ORGANIZATION": "crwdns10474:0crwdne10474:0", "EXPORT_DATA": "crwdns11197:0crwdne11197:0", "IMPORT_DATA": "crwdns11199:0crwdne11199:0", "ALL_ENTITIES": "crwdns11203:0crwdne11203:0"}, "TAGS": "crwdns2861:0crwdne2861:0", "LANGUAGE": "crwdns4646:0crwdne4646:0", "LANGUAGES": "crwdns4648:0crwdne4648:0", "EQUIPMENT": "crwdns2995:0crwdne2995:0", "EQUIPMENT_SHARING": "crwdns3122:0crwdne3122:0", "TASKS": "crwdns3027:0crwdne3027:0", "TASKS_SETTINGS": "crwdns7244:0crwdne7244:0", "INVOICES": "crwdns3272:0crwdne3272:0", "ORGANIZATION": "crwdns3394:0crwdne3394:0", "TENANT": "crwdns8718:0crwdne8718:0", "RECURRING_INVOICES": "crwdns4768:0crwdne4768:0", "INVOICES_RECEIVED": "crwdns4770:0crwdne4770:0", "ESTIMATES_RECEIVED": "crwdns4772:0crwdne4772:0", "ESTIMATES": "crwdns3398:0crwdne3398:0", "MY_TASKS": "crwdns3400:0crwdne3400:0", "JOBS": "crwdns8156:0crwdne8156:0", "PROPOSAL_TEMPLATE": "crwdns8306:0crwdne8306:0", "JOBS_SEARCH": "crwdns8190:0crwdne8190:0", "JOBS_MATCHING": "crwdns8192:0crwdne8192:0", "TEAM_TASKS": "crwdns3402:0crwdne3402:0", "TIME_ACTIVITY": "crwdns8358:0crwdne8358:0", "TIMESHEETS": "crwdns3406:0crwdne3406:0", "SCHEDULES": "crwdns3408:0crwdne3408:0", "EMAIL_TEMPLATES": "crwdns3410:0crwdne3410:0", "REPORTS": "crwdns3412:0crwdne3412:0", "GOALS": "crwdns4684:0crwdne4684:0", "ALL_REPORTS": "crwdns8276:0crwdne8276:0", "TIME_REPORTS": "crwdns8238:0crwdne8238:0", "WEEKLY_TIME_REPORTS": "crwdns8240:0crwdne8240:0", "ACCOUNTING_REPORTS": "crwdns3416:0crwdne3416:0", "PAYMENT_GATEWAYS": "crwdns3418:0crwdne3418:0", "SMS_GATEWAYS": "crwdns8384:0crwdne8384:0", "CUSTOM_SMTP": "crwdns3420:0crwdne3420:0", "INVENTORY": "crwdns3588:0crwdne3588:0", "SALES": "crwdns3536:0crwdne3536:0", "PAYMENTS": "crwdns3538:0crwdne3538:0", "FEATURES": "crwdns8764:0crwdne8764:0", "ACCOUNTING_TEMPLATES": "crwdns10166:0crwdne10166:0", "FOCUS": "crwdns11138:0crwdne11138:0", "APPLICATIONS": "crwdns11140:0crwdne11140:0"}, "SETTINGS_MENU": {"THEMES": "crwdns2106:0crwdne2106:0", "LIGHT": "crwdns2108:0crwdne2108:0", "DARK": "crwdns2110:0crwdne2110:0", "COSMIC": "crwdns2112:0crwdne2112:0", "CORPORATE": "crwdns2114:0crwdne2114:0", "MATERIAL_LIGHT_THEME": "crwdns10948:0crwdne10948:0", "MATERIAL_DARK_THEME": "crwdns10950:0crwdne10950:0", "GAUZY_LIGHT": "crwdns11000:0crwdne11000:0", "GAUZY_DARK": "crwdns11002:0crwdne11002:0", "LANGUAGE": "crwdns2116:0crwdne2116:0", "ENGLISH": "crwdns2118:0crwdne2118:0", "BULGARIAN": "crwdns2120:0crwdne2120:0", "HEBREW": "crwdns2122:0crwdne2122:0", "RUSSIAN": "crwdns2124:0crwdne2124:0", "PREFERRED_LAYOUT": "crwdns7048:0crwdne7048:0", "PREFERRED_LAYOUT_TOOLTIP": "crwdns7050:0crwdne7050:0", "RESET_LAYOUT": "crwdns7052:0crwdne7052:0", "RESET_LAYOUT_TOOLTIP": "crwdns7054:0crwdne7054:0", "TABLE": "crwdns7056:0crwdne7056:0", "CARDS_GRID": "crwdns7058:0crwdne7058:0", "SPRINT_VIEW": "crwdns8236:0crwdne8236:0", "QUICK_SETTINGS": "crwdns11022:0crwdne11022:0"}, "CHANGELOG_MENU": {"HEADER": "crwdns10190:0crwdne10190:0", "LEARN_MORE_URL": "crwdns10192:0crwdne10192:0", "GAUZY_FEATURES": "crwdns11168:0crwdne11168:0"}, "REPORT_PAGE": {"MEMBERS_WORKED": "crwdns8242:0crwdne8242:0", "MANUAL_TIME_EDIT_REPORT": "crwdns8488:0crwdne8488:0", "GROUP_BY": "crwdns11056:0crwdne11056:0", "DATE": "crwdns8470:0crwdne8470:0", "TO_DO": "crwdns11066:0crwdne11066:0", "TIME": "crwdns8474:0crwdne8474:0", "PROJECT": "crwdns8476:0crwdne8476:0", "CLIENT": "crwdns8478:0crwdne8478:0", "PROJECTS_WORKED": "crwdns8274:0crwdne8274:0", "APPS_AND_URLS_REPORT": "crwdns10266:0crwdne10266:0", "ACTIVITY": "crwdns8246:0crwdne8246:0", "TOTAL_HOURS": "crwdns8248:0crwdne8248:0", "EMPLOYEE": "crwdns8250:0crwdne8250:0", "EMPLOYEES/TEAMS": "crwdns11120:0crwdne11120:0", "NO_PROJECT": "crwdns8252:0crwdne8252:0", "NO_EMPLOYEE": "crwdns8254:0crwdne8254:0", "TITLE": "crwdns8484:0crwdne8484:0", "ACTION": "crwdns8492:0crwdne8492:0", "TIME_SPAN": "crwdns8494:0crwdne8494:0", "REASON": "crwdns8496:0crwdne8496:0", "CHANGED_AT": "crwdns8498:0crwdne8498:0", "DURATION": "crwdns8486:0crwdne8486:0", "NO_TASK": "crwdns8258:0crwdne8258:0", "FROM": "crwdns11068:0crwdne11068:0", "WEEKLY_TIME_AND_ACTIVITY_REPORT": "crwdns8260:0crwdne8260:0", "TIME_AND_ACTIVITY_REPORT": "crwdns8262:0crwdne8262:0", "NO_CLIENT": "crwdns8264:0crwdne8264:0", "ALL_REPORTS": "crwdns8294:0crwdne8294:0", "EXPENSES_REPORT": "crwdns8516:0crwdne8516:0", "CATEGORY": "crwdns8518:0crwdne8518:0", "DESCRIPTION": "crwdns8520:0crwdne8520:0", "AMOUNT": "crwdns8522:0crwdne8522:0", "NO_EXPENSES": "crwdns8524:0crwdne8524:0", "PAYMENT_REPORT": "crwdns8526:0crwdne8526:0", "NO_PAYMENTS": "crwdns8528:0crwdne8528:0", "CONTACT": "crwdns8530:0crwdne8530:0", "CURRENCY": "crwdns8532:0crwdne8532:0", "NOTE": "crwdns8534:0crwdne8534:0", "AMOUNT_OWED": "crwdns8542:0crwdne8542:0", "CURRENT_RATE": "crwdns8546:0crwdne8546:0", "HOURS": "crwdns8548:0crwdne8548:0", "SPENT": "crwdns9026:0crwdne9026:0", "BUDGET": "crwdns9028:0crwdne9028:0", "REMAINING": "crwdns9030:0crwdne9030:0", "WEEKLY_LIMIT_REPORT": "crwdns9032:0crwdne9032:0", "NO_EMPLOYEES": "crwdns9034:0crwdne9034:0", "DAILY_LIMIT_REPORT": "crwdns9036:0crwdne9036:0", "LIMIT": "crwdns9038:0crwdne9038:0", "SPENT_HOURS": "crwdns9040:0crwdne9040:0", "REMAINING_HOURS": "crwdns9042:0crwdne9042:0", "PROJECT_BUDGET_REPORTS": "crwdns9044:0crwdne9044:0", "CLIENT_BUDGET_REPORTS": "crwdns9048:0crwdne9048:0", "EXPENSE": "crwdns10517:0crwdne10517:0", "PAYMENT": "crwdns10264:0crwdne10264:0", "NO_EMPLOYEES_WORKED": "crwdns9496:0crwdne9496:0", "WEEKLY_TOTAL": "crwdns11193:0crwdne11193:0", "NO_DATA": {"APP_AND_URL_ACTIVITY": "crwdns11421:0crwdne11421:0", "MANUAL_ACTIVITY": "crwdns11423:0crwdne11423:0", "AMOUNT_OWED": "crwdns11425:0crwdne11425:0", "WEEKLY_TIME_AND_ACTIVITY": "crwdns11427:0crwdne11427:0", "DAILY_TIME_AND_ACTIVITY": "crwdns11429:0crwdne11429:0", "PROJECT_BUDGET": "crwdns11431:0crwdne11431:0", "CLIENT_BUDGET": "crwdns11433:0crwdne11433:0"}}, "INTEGRATIONS": {"AVAILABLE_INTEGRATIONS": "crwdns8266:0crwdne8266:0", "ADDED_UPWORK_TRANSACTION": "crwdns3202:0crwdne3202:0", "TOTAL_UPWORK_TRANSACTIONS_SUCCEED": "crwdns3204:0{{ totalExpenses }}crwdnd3204:0{{ totalIncomes }}crwdne3204:0", "HUBSTAFF_PAGE": {"NAME": "crwdns3558:0crwdne3558:0", "SELECT_ORGANIZATION": "crwdns3560:0crwdne3560:0", "SYNCED_PROJECTS": "crwdns3562:0crwdne3562:0", "SETTINGS_UPDATED": "crwdns3728:0crwdne3728:0", "SYNCED_ENTITIES": "crwdns3730:0crwdne3730:0", "TOOLTIP_ACTIVITY_INFO": "crwdns3916:0crwdne3916:0", "DATE_RANGE_PLACEHOLDER": "crwdns3918:0crwdne3918:0", "CLIENT_ID": "crwdns7018:0crwdne7018:0", "CLIENT_SECRET": "crwdns7020:0crwdne7020:0", "GRANT_PERMISSION": "crwdns9498:0crwdne9498:0", "ENTER_CLIENT_SECRET": "crwdns9500:0crwdne9500:0"}, "UPWORK_PAGE": {"ACTIVITIES": "crwdns4192:0crwdne4192:0", "REPORTS": "crwdns4194:0crwdne4194:0", "TRANSACTIONS": "crwdns4196:0crwdne4196:0", "SUCCESSFULLY_AUTHORIZED": "crwdns4198:0crwdne4198:0", "API_KEY": "crwdns4200:0crwdne4200:0", "SECRET": "crwdns4202:0crwdne4202:0", "NEXT_STEP_INFO": "crwdns4204:0crwdne4204:0", "CONTRACTS": "crwdns4326:0crwdne4326:0", "SYNCED_CONTRACTS": "crwdns4328:0crwdne4328:0", "SELECT_DATE": "crwdns4410:0crwdne4410:0", "ONLY_CONTRACTS": "crwdns4412:0crwdne4412:0", "CONTRACTS_RELATED_DATA": "crwdns4414:0crwdne4414:0", "DATE_RANGE_PLACEHOLDER": "crwdns7782:0crwdne7782:0", "HOURLY": "crwdns10004:0crwdne10004:0"}, "COMING_SOON": "crwdns4078:0crwdne4078:0", "RE_INTEGRATE": "crwdns8132:0crwdne8132:0", "SETTINGS": "crwdns8134:0crwdne8134:0", "SELECT_GROUPS": "crwdns8268:0crwdne8268:0", "FILTER_INTEGRATIONS": "crwdns8270:0crwdne8270:0", "SEARCH_INTEGRATIONS": "crwdns8272:0crwdne8272:0", "PAID": "crwdns11205:0crwdne11205:0", "INTEGRATION": "crwdns11207:0crwdne11207:0"}, "DASHBOARD_PAGE": {"ACCOUNTING": "crwdns3190:0crwdne3190:0", "HUMAN_RESOURCES": "crwdns3192:0crwdne3192:0", "TIME_TRACKING": "crwdns3194:0crwdne3194:0", "PROJECT_MANAGEMENT": "crwdns3196:0crwdne3196:0", "EMPLOYEE_STATISTICS": "crwdns194:0crwdne194:0", "SELECT_A_MONTH_AND_EMPLOYEE": "crwdns196:0crwdne196:0", "INSERT_TEXT_FOR_NOT_AUTHENTICATED_USERS": "crwdns2633:0crwdne2633:0", "CHARTS": {"BAR": "crwdns3206:0crwdne3206:0", "DOUGHNUT": "crwdns3208:0crwdne3208:0", "STACKED_BAR": "crwdns3210:0crwdne3210:0", "CHART_TYPE": "crwdns3212:0crwdne3212:0", "REVENUE": "crwdns3214:0crwdne3214:0", "EXPENSES": "crwdns3216:0crwdne3216:0", "PROFIT": "crwdns3218:0crwdne3218:0", "BONUS": "crwdns3220:0crwdne3220:0", "NO_MONTH_DATA": "crwdns3222:0crwdne3222:0", "CASH_FLOW": "crwdns11415:0crwdne11415:0", "WORKING": "crwdns11511:0crwdne11511:0", "WORKING_NOW": "crwdns11513:0crwdne11513:0", "NOT_WORKING": "crwdns11515:0crwdne11515:0", "WORKING_TODAY": "crwdns11534:0crwdne11534:0"}, "PROFIT_HISTORY": {"PROFIT_REPORT": "crwdns2585:0crwdne2585:0", "TOTAL_EXPENSES": "crwdns2587:0crwdne2587:0", "TOTAL_INCOME": "crwdns2589:0crwdne2589:0", "TOTAL_PROFIT": "crwdns2591:0crwdne2591:0", "DATE": "crwdns9502:0crwdne9502:0", "EXPENSES": "crwdns9504:0crwdne9504:0", "INCOME": "crwdns9506:0crwdne9506:0", "DESCRIPTION": "crwdns10476:0crwdne10476:0"}, "TITLE": {"PROFIT_REPORT": "crwdns2593:0crwdne2593:0", "TOTAL_EXPENSES": "crwdns2595:0crwdne2595:0", "TOTAL_INCOME": "crwdns2597:0crwdne2597:0", "PROFIT": "crwdns2599:0crwdne2599:0", "TOTAL_BONUS": "crwdns2601:0crwdne2601:0", "TOTAL_DIRECT_INCOME": "crwdns2877:0crwdne2877:0", "SALARY": "crwdns2899:0crwdne2899:0", "TOTAL_DIRECT_INCOME_INFO": "crwdns2901:0crwdne2901:0", "TOTAL_INCOME_CALC": "crwdns2931:0{{ totalNonBonusIncome }}crwdnd2931:0{{ totalBonusIncome }}crwdne2931:0", "TOTAL_PROFIT_BONUS": "crwdns2905:0crwdne2905:0", "TOTAL_DIRECT_BONUS": "crwdns2907:0crwdne2907:0", "TOTAL_DIRECT_BONUS_INFO": "crwdns2909:0crwdne2909:0", "TOTAL_PROFIT_BONUS_INFO": "crwdns2949:0{{ bonusPercentage }}crwdnd2949:0{{ difference }}crwdne2949:0", "TOTAL_INCOME_BONUS": "crwdns2913:0crwdne2913:0", "TOTAL_INCOME_BONUS_INFO": "crwdns2951:0{{ bonusPercentage }}crwdnd2951:0{{ totalIncome }}crwdne2951:0", "TOTAL_EXPENSE_CALC": "crwdns2917:0crwdne2917:0", "TOTAL_EXPENSES_WITHOUT_SALARY": "crwdns2953:0crwdne2953:0", "TOTAL_EXPENSES_WITHOUT_SALARY_CALC": "crwdns2955:0crwdne2955:0", "TOTAL_BONUS_CALC": "crwdns2957:0{{ totalBonusIncome }}crwdnd2957:0{{ calculatedBonus }}crwdne2957:0"}, "DEVELOPER": {"DEVELOPER": "crwdns198:0crwdne198:0", "AVERAGE_BONUS": "crwdns2327:0crwdne2327:0", "TOTAL_INCOME": "crwdns202:0crwdne202:0", "TOTAL_EXPENSES": "crwdns204:0crwdne204:0", "PROFIT": "crwdns206:0crwdne206:0", "PROFIT_CALC": "crwdns2933:0{{ totalAllIncome }}crwdnd2933:0{{ totalExpense }}crwdne2933:0", "NOTE": "crwdns210:0crwdne210:0", "BONUS": "crwdns212:0crwdne212:0", "EMPLOYEES": "crwdns11417:0crwdne11417:0"}, "ADD_INCOME": "crwdns2761:0crwdne2761:0", "ADD_EXPENSE": "crwdns2763:0crwdne2763:0", "RECURRING_EXPENSES": "crwdns2925:0crwdne2925:0", "ADD_ORGANIZATION_RECURRING_EXPENSE": "crwdns2927:0crwdne2927:0", "ADD_EMPLOYEE_RECURRING_EXPENSE": "crwdns2929:0crwdne2929:0", "PLAN_MY_DAY": "crwdns11467:0crwdne11467:0", "ADD_TODO": "crwdns11469:0crwdne11469:0", "MOST_VIEW_PROJECTS": "crwdns11471:0crwdne11471:0", "INBOX": "crwdns11473:0crwdne11473:0", "RECENTLY_ASSIGNED": "crwdns11475:0crwdne11475:0", "NO_TODO_ASSIGNED": "crwdns11477:0crwdne11477:0"}, "INCOME_PAGE": {"INCOME": "crwdns214:0crwdne214:0", "BONUS_HELP": "crwdns2465:0crwdne2465:0", "BONUS_TOOLTIP": "crwdns2467:0crwdne2467:0", "EMPLOYEES_GENERATE_INCOME": "crwdns7682:0crwdne7682:0"}, "EXPENSES_PAGE": {"EXPENSES": "crwdns216:0crwdne216:0", "MUTATION": {"CONTACT_IS_REQUIRED": "crwdns7950:0crwdne7950:0", "PLEASE_SELECT_A_CONTACT_OR_CHANGE_EXPENSE_TYPE": "crwdns7490:0crwdne7490:0", "ASSIGN_TO": "crwdns2553:0crwdne2553:0", "INCLUDE_TAXES": "crwdns2555:0crwdne2555:0", "ATTACH_A_RECEIPT": "crwdns2557:0crwdne2557:0", "EMPLOYEES_GENERATE_EXPENSE": "crwdns7684:0crwdne7684:0", "TAX_DEDUCTIBLE": "crwdns10006:0crwdne10006:0", "NOT_TAX_DEDUCTIBLE": "crwdns10008:0crwdne10008:0", "BILLABLE_TO_CONTACT": "crwdns10010:0crwdne10010:0", "PERCENTAGE": "crwdns10012:0crwdne10012:0", "VALUE": "crwdns10014:0crwdne10014:0", "TAX_AMOUNT": "crwdns10016:0crwdne10016:0", "TAX_RATE": "crwdns10018:0crwdne10018:0", "INVOICED": "crwdns10020:0crwdne10020:0", "UNINVOICED": "crwdns10022:0crwdne10022:0", "PAID": "crwdns10024:0crwdne10024:0", "NOT_BILLABLE": "crwdns10026:0crwdne10026:0"}, "DEFAULT_CATEGORY": {"SALARY": "crwdns2881:0crwdne2881:0", "SALARY_TAXES": "crwdns2883:0crwdne2883:0", "RENT": "crwdns2885:0crwdne2885:0", "EXTRA_BONUS": "crwdns2887:0crwdne2887:0"}, "SPLIT_HELP": "crwdns2889:0crwdne2889:0", "SPLIT_WILL_BE_TOOLTIP": "crwdns2891:0crwdne2891:0", "SPLIT_EXPENSE": "crwdns2893:0crwdne2893:0", "ADD_EXPENSE_CATEGORY": "crwdns3270:0crwdne3270:0", "EXPENSE_CATEGORY": "crwdns9510:0crwdne9510:0", "RECURRING_EXPENSES": {"WARNING": "crwdns9512:0crwdne9512:0", "FROM": "crwdns9514:0crwdne9514:0", "TO": "crwdns9516:0crwdne9516:0", "VALUE_OVERWRITTEN": "crwdns9518:0crwdne9518:0", "ERROR": "crwdns9520:0crwdne9520:0", "NOT_SUPPORTED": "crwdns9522:0crwdne9522:0", "EDIT_FUTURE_VALUE": "crwdns9524:0crwdne9524:0", "EXISTING_VALUE": "crwdns9526:0crwdne9526:0", "STARTED_ON": "crwdns9528:0crwdne9528:0", "AFFECTED": "crwdns9530:0crwdne9530:0", "SET_EXPENSE_VALUE": "crwdns9532:0crwdne9532:0", "ONWARDS": "crwdns9534:0crwdne9534:0", "ENDING_ON": "crwdns9536:0crwdne9536:0", "SET_UNTIL": "crwdns9538:0crwdne9538:0", "REDUCE_START_DATE": "crwdns9540:0crwdne9540:0", "FOR_EXPENSE_VALUE": "crwdns9542:0crwdne9542:0", "CHANGE_START_DATE": "crwdns9544:0crwdne9544:0"}}, "EMPLOYEES_PAGE": {"HEADER": "crwdns10268:0crwdne10268:0", "ADD_EMPLOYEE": "crwdns220:0crwdne220:0", "ACTIVE": "crwdns222:0crwdne222:0", "END_WORK": "crwdns224:0crwdne224:0", "WORK_ENDED": "crwdns226:0crwdne226:0", "DELETED": "crwdns2301:0crwdne2301:0", "ENABLED": "crwdns10936:0crwdne10936:0", "DISABLED": "crwdns10938:0crwdne10938:0", "RECURRING_EXPENSE": "crwdns2477:0crwdne2477:0", "RECURRING_EXPENSE_EDIT": "crwdns2479:0crwdne2479:0", "RECURRING_EXPENSE_ADD": "crwdns2481:0crwdne2481:0", "RECURRING_EXPENSE_SET": "crwdns9850:0{{ name }}crwdne9850:0", "RECURRING_EXPENSE_EDITED": "crwdns9852:0{{ name }}crwdne9852:0", "RECURRING_EXPENSE_DELETED": "crwdns9854:0{{ name }}crwdne9854:0", "EMPLOYEE_NAME": "crwdns9552:0crwdne9552:0", "BACK_TO_WORK": "crwdns230:0crwdne230:0", "SELECT_EMPLOYEE_MSG": "crwdns2559:0crwdne2559:0", "EDIT_EMPLOYEE": {"HEADER": "crwdns2163:0crwdne2163:0", "DEVELOPER": "crwdns232:0crwdne232:0", "DEPARTMENT": "crwdns234:0crwdne234:0", "POSITION": "crwdns236:0crwdne236:0", "EMPLOYEE_DEPARTMENTS": "crwdns2515:0crwdne2515:0", "EMPLOYEE_PROJECTS": "crwdns2517:0crwdne2517:0", "EMPLOYEE_CONTACTS": "crwdns5108:0crwdne5108:0", "EMPLOYMENT_TYPE": "crwdns3071:0crwdne3071:0", "ACCOUNT": "crwdns3176:0crwdne3176:0", "EMPLOYMENT": "crwdns3178:0crwdne3178:0", "LOCATION": "crwdns3180:0crwdne3180:0", "RATES": "crwdns3182:0crwdne3182:0", "PROJECTS": "crwdns3184:0crwdne3184:0", "CONTACTS": "crwdns5110:0crwdne5110:0", "HIRING": "crwdns3188:0crwdne3188:0", "NETWORKS": "crwdns8640:0crwdne8640:0", "EMPLOYEE_LEVEL": "crwdns8668:0crwdne8668:0", "DISPLAY_BONUS_ANONYMOUSLY": "crwdns8670:0crwdne8670:0", "JOB_SUCCESS": "crwdns8672:0crwdne8672:0", "TOTAL_JOBS": "crwdns8674:0crwdne8674:0", "TOTAL_HOURS": "crwdns8676:0crwdne8676:0", "RATE": "crwdns8678:0crwdne8678:0", "VETTED": "crwdns8680:0crwdne8680:0", "HR": "crwdns8682:0crwdne8682:0"}, "ADD_EMPLOYEES": {"STEP_1": "crwdns3081:0crwdne3081:0", "STEP_2": "crwdns3083:0crwdne3083:0", "STEP_3": "crwdns10881:0crwdne10881:0", "ADD_ANOTHER_EMPLOYEE": "crwdns3085:0crwdne3085:0", "FINISHED_ADDING": "crwdns3087:0crwdne3087:0", "NEXT": "crwdns3089:0crwdne3089:0", "PREVIOUS": "crwdns3091:0crwdne3091:0"}, "NOT_STARTED": "crwdns3564:0crwdne3564:0", "NOT_STARTED_HELP": "crwdns3566:0crwdne3566:0"}, "GOALS_PAGE": {"HEADER": "crwdns10270:0crwdne10270:0", "GOAL": "crwdns4976:0crwdne4976:0", "GOALS_EMPTY": "crwdns4924:0crwdne4924:0", "ADD_NEW_KEY_RESULT": "crwdns4926:0crwdne4926:0", "ADD_NEW_OBJECTIVE": "crwdns4934:0crwdne4934:0", "EDIT_OBJECTIVE": "crwdns7452:0crwdne7452:0", "SESSION": "crwdns4978:0crwdne4978:0", "GOAL_SETTINGS": "crwdns7638:0crwdne7638:0", "NO_DESCRIPTION": "crwdns4980:0crwdne4980:0", "PROGRESS": "crwdns4982:0crwdne4982:0", "EXPECTED": "crwdns9554:0crwdne9554:0", "UPDATES": "crwdns4984:0crwdne4984:0", "UPDATE": "crwdns4986:0crwdne4986:0", "COMMENTS": "crwdns4988:0crwdne4988:0", "KEY_RESULTS": "crwdns4990:0crwdne4990:0", "GROUP_BY": "crwdns7454:0crwdne7454:0", "DELETE_OBJECTIVE": "crwdns9556:0crwdne9556:0", "DELETE_KEY_RESULT": "crwdns9558:0crwdne9558:0", "ARE_YOU_SURE": "crwdns9560:0crwdne9560:0", "ALL_OBJECTIVES": "crwdns9562:0crwdne9562:0", "MY_TEAMS_OBJECTIVES": "crwdns9564:0crwdne9564:0", "MY_ORGANIZATIONS_OBJECTIVES": "crwdns9566:0crwdne9566:0", "MY_OBJECTIVES": "crwdns9568:0crwdne9568:0", "OBJECTIVE_LEVEL": "crwdns9570:0crwdne9570:0", "TIME_FRAME": "crwdns9572:0crwdne9572:0", "CREATE_NEW": "crwdns9574:0crwdne9574:0", "GOAL_TEMPLATES": "crwdns9576:0crwdne9576:0", "CREATE_NEW_MENU": "crwdns11247:0crwdne11247:0", "CREATE_FROM_PRESET": "crwdns11249:0crwdne11249:0", "OWNERSHIP": {"EMPLOYEES": "crwdns7500:0crwdne7500:0", "TEAMS": "crwdns7502:0crwdne7502:0", "EMPLOYEES_AND_TEAMS": "crwdns7504:0crwdne7504:0"}, "SETTINGS": {"ADD_TIME_FRAME_TITLE": "crwdns4992:0crwdne4992:0", "EDIT_TIME_FRAME_TITLE": "crwdns10585:0crwdne10585:0", "TIME_FRAME_PAGE_TITLE": "crwdns4994:0crwdne4994:0", "PREDEFINED_TIME_FRAMES": "crwdns4996:0crwdne4996:0", "ADD_KPI": "crwdns7346:0crwdne7346:0", "EDIT_KPI": "crwdns10587:0crwdne10587:0", "MAX_ENTITIES": "crwdns8406:0crwdne8406:0", "EMPLOYEE_OBJECTIVES": "crwdns7508:0crwdne7508:0", "WHO_CAN_OWN_OBJECTIVES": "crwdns7510:0crwdne7510:0", "WHO_CAN_OWN_KEY_RESULTS": "crwdns7512:0crwdne7512:0", "ADD_KPI_TO_KEY_RESULT": "crwdns7514:0crwdne7514:0", "ADD_TASK_TO_KEY_RESULT": "crwdns7516:0crwdne7516:0", "GENERAL": "crwdns8152:0crwdne8152:0", "KPI": "crwdns8154:0crwdne8154:0", "DELETE_TIME_FRAME_TITLE": "crwdns8776:0crwdne8776:0", "DELETE_TIME_FRAME_CONFIRMATION": "crwdns8778:0crwdne8778:0", "DELETE_KPI_TITLE": "crwdns8780:0crwdne8780:0", "DELETE_KPI_CONFIRMATION": "crwdns8782:0crwdne8782:0", "ANNUAL": "crwdns9578:0crwdne9578:0"}, "MESSAGE": {"NO_KEY_RESULT": "crwdns7418:0crwdne7418:0", "NO_UPDATES": "crwdns7420:0crwdne7420:0", "NO_ALIGNMENT": "crwdns7422:0crwdne7422:0"}, "LEVELS": {"ORGANIZATION": "crwdns4998:0crwdne4998:0", "TEAM": "crwdns5000:0crwdne5000:0", "EMPLOYEE": "crwdns5002:0crwdne5002:0"}, "TIME_FRAME_STATUS": {"ACTIVE": "crwdns5004:0crwdne5004:0", "INACTIVE": "crwdns5006:0crwdne5006:0"}, "KPI_OPERATOR": {"GREATER_THAN_EQUAL_TO": "crwdns7348:0crwdne7348:0", "LESSER_THAN_EQUAL_TO": "crwdns7350:0crwdne7350:0"}, "KPI_METRIC": {"NUMERICAL": "crwdns7686:0crwdne7686:0", "PERCENTAGE": "crwdns7354:0crwdne7354:0", "CURRENCY": "crwdns7688:0crwdne7688:0"}, "TOOLTIPS": {"PROGRESS": "crwdns7690:0crwdne7690:0", "DETAILS": "crwdns7692:0crwdne7692:0", "EDIT": "crwdns7694:0crwdne7694:0"}, "FORM": {"LABELS": {"LEVEL": "crwdns5008:0crwdne5008:0", "OWNER": "crwdns5010:0crwdne5010:0", "LEAD": "crwdns7376:0crwdne7376:0", "LEAD_OPTIONAL": "crwdns7378:0crwdne7378:0", "DEADLINE": "crwdns5014:0crwdne5014:0", "STATUS": "crwdns5016:0crwdne5016:0", "KPI_SHOULD_BE": "crwdns7356:0crwdne7356:0", "KPI_METRIC": "crwdns7640:0crwdne7640:0", "CURRENT_VALUE": "crwdns7358:0crwdne7358:0", "OBJECTIVE": "crwdns7518:0crwdne7518:0", "KEY_RESULT": "crwdns7520:0crwdne7520:0"}, "PLACEHOLDERS": {"NAME": "crwdns5018:0crwdne5018:0", "DESCRIPTION": "crwdns5020:0crwdne5020:0", "LEVEL": "crwdns5022:0crwdne5022:0", "TIME_FRAME_NAME": "crwdns5024:0crwdne5024:0", "KPI_DESCRIPTION": "crwdns8408:0crwdne8408:0", "KPI_NAME": "crwdns7362:0crwdne7362:0"}, "ERROR": {"START_DATE_GREATER": "crwdns5026:0crwdne5026:0"}}, "BUTTONS": {"ADD_TIME_FRAME": "crwdns5028:0crwdne5028:0"}, "HELPER_TEXT": {"OBJECTIVE_GENERAL": "crwdns7642:0crwdne7642:0", "OBJECTIVE_TITLE": "crwdns7644:0crwdne7644:0", "OBJECTIVE_DESCRIPTION": "crwdns7646:0crwdne7646:0", "OBJECTIVE_LEVEL": "crwdns7648:0crwdne7648:0", "OBJECTIVE_OWNER": "crwdns7650:0crwdne7650:0", "OBJECTIVE_LEAD": "crwdns7652:0crwdne7652:0", "OBJECTIVE_TIMEFRAME": "crwdns7654:0crwdne7654:0", "KPI_GENERAL": "crwdns7656:0crwdne7656:0", "KPI_NAME": "crwdns7658:0crwdne7658:0", "KPI_DESCRIPTION": "crwdns7660:0crwdne7660:0", "KPI_METRIC": "crwdns7662:0crwdne7662:0", "KPI_LEAD": "crwdns7664:0crwdne7664:0"}}, "KEY_RESULT_PAGE": {"UPDATE_KEY_RESULT": "crwdns5030:0crwdne5030:0", "EDIT_KEY_RESULT": "crwdns7398:0crwdne7398:0", "EDIT_KEY_RESULT_PARAMETERS": "crwdns11251:0crwdne11251:0", "ADD_KEY_RESULT": "crwdns7400:0crwdne7400:0", "UPDATE": {"STATUS": {"ON_TRACK": "crwdns4936:0crwdne4936:0", "NEEDS_ATTENTION": "crwdns4938:0crwdne4938:0", "OFF_TRACK": "crwdns4940:0crwdne4940:0", "NONE": "crwdns4942:0crwdne4942:0"}}, "WEIGHT": {"DEFAULT": "crwdns7154:0crwdne7154:0", "INCREASE_BY_2X": "crwdns7156:0crwdne7156:0", "INCREASE_BY_4X": "crwdns7170:0crwdne7170:0", "MESSAGE": "crwdns7172:0crwdne7172:0", "OBJECTIVE_PROGRESS": "crwdns7174:0{{ weight }}crwdne7174:0"}, "MESSAGE": {"TIME_FRAME_ENDED": "crwdns7118:0{{ date }}crwdne7118:0", "TIME_FRAME_NOT_STARTED": "crwdns7120:0{{ date }}crwdne7120:0"}, "TYPE": {"NUMERICAL": "crwdns7696:0crwdne7696:0", "TRUE_OR_FALSE": "crwdns5034:0crwdne5034:0", "CURRENCY": "crwdns5036:0crwdne5036:0", "TASK": "crwdns5038:0crwdne5038:0", "KPI": "crwdns7476:0crwdne7476:0"}, "DEADLINE": {"NO_CUSTOM_DEADLINE": "crwdns5040:0crwdne5040:0", "HARD_DEADLINE": "crwdns5042:0crwdne5042:0", "HARD_AND_SOFT_DEADLINE": "crwdns5044:0crwdne5044:0"}, "TOOLTIPS": {"PROGRESS": "crwdns7698:0{{weight}}crwdne7698:0", "DETAILS": "crwdns7700:0crwdne7700:0", "EDIT": "crwdns7702:0crwdne7702:0", "WEIGHT": "crwdns7704:0crwdne7704:0"}, "FORM": {"LABELS": {"KEY_RESULT_TYPE": "crwdns5046:0crwdne5046:0", "INITIAL_VALUE": "crwdns5048:0crwdne5048:0", "TARGET_VALUE": "crwdns5050:0crwdne5050:0", "OWNER": "crwdns5052:0crwdne5052:0", "LEAD": "crwdns5054:0crwdne5054:0", "DEADLINE": "crwdns5056:0crwdne5056:0", "SOFT_DEADLINE": "crwdns5058:0crwdne5058:0", "HARD_DEADLINE": "crwdns5060:0crwdne5060:0", "UPDATED_VALUE": "crwdns5062:0crwdne5062:0", "MARK_COMPLETE": "crwdns5064:0crwdne5064:0", "STATUS": "crwdns5066:0crwdne5066:0", "WEIGHT": "crwdns7160:0crwdne7160:0", "TYPE": "crwdns11253:0crwdne11253:0", "SELECT_PROJECT": "crwdns7214:0crwdne7214:0", "SELECT_TASK": "crwdns7216:0crwdne7216:0", "SELECT_KPI": "crwdns7478:0crwdne7478:0", "ASSIGN_AS_OBJECTIVE": "crwdns7402:0crwdne7402:0"}, "PLACEHOLDERS": {"NAME": "crwdns5068:0crwdne5068:0", "DESCRIPTION": "crwdns8410:0crwdne8410:0"}}, "HELPER_TEXT": {"KEY_RESULT_GENERAL": "crwdns7666:0crwdne7666:0", "KEY_RESULT_OWNER": "crwdns7668:0crwdne7668:0", "KEY_RESULT_LEAD": "crwdns7670:0crwdne7670:0"}}, "CANDIDATES_PAGE": {"HEADER": "crwdns10272:0crwdne10272:0", "ADD_CANDIDATE": "crwdns4120:0crwdne4120:0", "APPLIED": "crwdns3334:0crwdne3334:0", "HIRED": "crwdns3336:0crwdne3336:0", "REJECTED": "crwdns3338:0crwdne3338:0", "DELETED": "crwdns3340:0crwdne3340:0", "ARCHIVED": "crwdns3940:0crwdne3940:0", "SELECT_EMPLOYEE_MSG": "crwdns3342:0crwdne3342:0", "JOBS_CANDIDATES": "crwdns8514:0crwdne8514:0", "SOURCE": "crwdns9580:0crwdne9580:0", "RATING": "crwdns9582:0crwdne9582:0", "STATUS": "crwdns9584:0crwdne9584:0", "EDIT_CANDIDATE": {"HEADER": "crwdns3506:0crwdne3506:0", "DEVELOPER": "crwdns3508:0crwdne3508:0", "DEPARTMENT": "crwdns3510:0crwdne3510:0", "POSITION": "crwdns3512:0crwdne3512:0", "CANDIDATE_DEPARTMENTS": "crwdns4122:0crwdne4122:0", "EMPLOYMENT_TYPE": "crwdns3516:0crwdne3516:0", "CANDIDATES_LEVEL": "crwdns8150:0crwdne8150:0", "ACCOUNT": "crwdns3518:0crwdne3518:0", "EMPLOYMENT": "crwdns3520:0crwdne3520:0", "LOCATION": "crwdns3522:0crwdne3522:0", "RATE": "crwdns3546:0crwdne3546:0", "HIRING": "crwdns3526:0crwdne3526:0", "EXPERIENCE": "crwdns3528:0crwdne3528:0", "SKILLS": "crwdns3692:0crwdne3692:0", "ALL_FEEDBACKS": "crwdns8100:0crwdne8100:0", "FEEDBACKS": "crwdns3870:0crwdne3870:0", "EDUCATION": "crwdns3694:0crwdne3694:0", "SCHOOL_NAME": "crwdns3696:0crwdne3696:0", "DEGREE": "crwdns3698:0crwdne3698:0", "FIELD": "crwdns3700:0crwdne3700:0", "COMPLETION_DATE": "crwdns3702:0crwdne3702:0", "ADDITIONAL_NOTES": "crwdns3704:0crwdne3704:0", "OCCUPATION": "crwdns3706:0crwdne3706:0", "ORGANIZATION": "crwdns3708:0crwdne3708:0", "DURATION": "crwdns3710:0crwdne3710:0", "DESCRIPTION": "crwdns3712:0crwdne3712:0", "TASKS": "crwdns3530:0crwdne3530:0", "HISTORY": "crwdns3532:0crwdne3532:0", "DOCUMENTS": "crwdns3534:0crwdne3534:0", "DOCUMENT_NAME": "crwdns3832:0crwdne3832:0", "NAME": "crwdns8114:0crwdne8114:0", "DOCUMENT": "crwdns3834:0crwdne3834:0", "FEEDBACK_DESCRIPTION": "crwdns3872:0crwdne3872:0", "INTERVIEW_FEEDBACK": "crwdns4370:0crwdne4370:0", "INTERVIEWER": "crwdns8102:0crwdne8102:0", "FEEDBACK_STATUS": "crwdns8104:0crwdne8104:0", "LEAVE_FEEDBACK": "crwdns4350:0crwdne4350:0", "STATUS": "crwdns4372:0crwdne4372:0", "INTERVIEW": {"INTERVIEW": "crwdns3994:0crwdne3994:0", "INTERVIEWS": "crwdns4056:0crwdne4056:0", "INTERVIEWER": "crwdns4374:0crwdne4374:0", "ADD_INTERVIEW": "crwdns4124:0crwdne4124:0", "HIDE_PAST": "crwdns4408:0crwdne4408:0", "ON": "crwdns4058:0crwdne4058:0", "TO": "crwdns4126:0crwdne4126:0", "FROM": "crwdns4060:0crwdne4060:0", "WITH": "crwdns4062:0crwdne4062:0", "SCHEDULE_INTERVIEW": "crwdns3996:0crwdne3996:0", "SCHEDULED_INTERVIEWS": "crwdns4318:0crwdne4318:0", "CONTINUE": "crwdns4484:0crwdne4484:0", "PAST_DATE": "crwdns4474:0crwdne4474:0", "EDIT_INTERVIEW": "crwdns4216:0crwdne4216:0", "STEP_1": "crwdns3998:0crwdne3998:0", "STEP_2": "crwdns4000:0crwdne4000:0", "STEP_3": "crwdns4720:0crwdne4720:0", "NEXT": "crwdns4002:0crwdne4002:0", "PREVIOUS": "crwdns4004:0crwdne4004:0", "SAVE": "crwdns4006:0crwdne4006:0", "CREATE_CRITERIONS": "crwdns4950:0crwdne4950:0", "EMAIL_NOTIFICATION": "crwdns4050:0crwdne4050:0", "DELETE_INTERVIEW": "crwdns7280:0crwdne7280:0", "DELETE_FEEDBACK": "crwdns7380:0crwdne7380:0", "DELETE_INTERVIEW_ARE_YOU_SURE": "crwdns7382:0crwdne7382:0", "DELETE_FEEDBACK_ARE_YOU_SURE": "crwdns7384:0crwdne7384:0", "SUMMARY": "crwdns4048:0crwdne4048:0", "DETAILS": "crwdns4128:0crwdne4128:0", "NOTIFY_CANDIDATE": "crwdns4130:0crwdne4130:0", "NOTIFY_INTERVIEWERS": "crwdns4132:0crwdne4132:0", "INTERVIEWERS": "crwdns4134:0crwdne4134:0", "HIRE": "crwdns4352:0crwdne4352:0", "RATING": "crwdns8106:0crwdne8106:0", "DESCRIPTION": "crwdns8108:0crwdne8108:0", "POSTPONE": "crwdns4354:0crwdne4354:0", "REJECT": "crwdns4356:0crwdne4356:0", "INTERVIEW_TITLE_EXIST": "crwdns7184:0crwdne7184:0", "SET_AS_ARCHIVED": "crwdns9586:0{{ title }}crwdne9586:0"}}, "INTERVIEW_INFO_MODAL": {"SCHEDULED": "crwdns4268:0crwdne4268:0", "HOURS_AGO": "crwdns4270:0crwdne4270:0", "LESS_MINUTE": "crwdns4304:0crwdne4304:0", "MINUTES_AGO": "crwdns4274:0crwdne4274:0", "DAYS_AGO": "crwdns4276:0crwdne4276:0", "DATE": "crwdns4278:0crwdne4278:0", "TIME": "crwdns4280:0crwdne4280:0", "CANDIDATE": "crwdns4282:0crwdne4282:0", "INTERVIEWERS": "crwdns4284:0crwdne4284:0", "LOCATION": "crwdns4286:0crwdne4286:0", "NOTE": "crwdns4288:0crwdne4288:0", "OF": "crwdns4290:0crwdne4290:0", "INTERVIEWS_LOWER_CASE": "crwdns4320:0crwdne4320:0"}, "ADD_CANDIDATES": {"STEP_1": "crwdns4136:0crwdne4136:0", "STEP_2": "crwdns4138:0crwdne4138:0", "STEP_3": "crwdns4140:0crwdne4140:0", "UPLOAD_CV": "crwdns4142:0crwdne4142:0", "ADD_ANOTHER_CANDIDATE": "crwdns4144:0crwdne4144:0", "FINISHED_ADDING": "crwdns4146:0crwdne4146:0", "NEXT": "crwdns4148:0crwdne4148:0", "PREVIOUS": "crwdns4150:0crwdne4150:0"}, "MANAGE_INTERVIEWS": {"CALENDAR": "crwdns5096:0crwdne5096:0", "INTERVIEWS": "crwdns5098:0crwdne5098:0", "MANAGE_INTERVIEWS": "crwdns5100:0crwdne5100:0", "CRITERIONS": "crwdns5102:0crwdne5102:0", "DEFAULT": "crwdns7284:0crwdne7284:0", "DATE": "crwdns7060:0crwdne7060:0", "SORT_BY": "crwdns7062:0crwdne7062:0", "SEARCH_BY_INTERVIEW": "crwdns7286:0crwdne7286:0", "SEARCH_BY_CANDIDATE": "crwdns7386:0crwdne7386:0", "CANDIDATE_NAME": "crwdns7064:0crwdne7064:0", "RATING": "crwdns7066:0crwdne7066:0", "RESET_FILTERS": "crwdns7388:0crwdne7388:0", "TITLE": "crwdns7596:0crwdne7596:0", "INTERVIEWERS": "crwdns7598:0crwdne7598:0", "ADD_FEEDBACK": "crwdns8026:0crwdne8026:0", "LOCATION": "crwdns7600:0crwdne7600:0", "NOTES": "crwdns7602:0crwdne7602:0", "ACTIONS": "crwdns7604:0crwdne7604:0", "EDIT": "crwdns8028:0crwdne8028:0", "ARCHIVE": "crwdns8030:0crwdne8030:0", "PAST": "crwdns8032:0crwdne8032:0", "DELETE": "crwdns8034:0crwdne8034:0", "UPDATED": "crwdns7606:0crwdne7606:0", "CANDIDATE": "crwdns7608:0crwdne7608:0", "FEEDBACK_PROHIBIT": "crwdns8036:0crwdne8036:0", "START_DATE": "crwdns8010:0crwdne8010:0"}, "STATISTIC": {"STATISTICS": "crwdns7588:0crwdne7588:0", "RATING": "crwdns7590:0crwdne7590:0", "INTERVIEWER_ASSESSMENT": "crwdns7592:0crwdne7592:0", "CRITERIONS_RATING": "crwdns7076:0crwdne7076:0", "CANDIDATE_CRITERIONS_RATING": "crwdns7594:0crwdne7594:0", "NO_DATA": "crwdns4570:0crwdne4570:0", "SELECT_INTERVIEW": "crwdns4572:0crwdne4572:0", "SELECT_INTERVIEW_INTERVIEWER": "crwdns7038:0crwdne7038:0", "SELECT_CANDIDATE": "crwdns7080:0crwdne7080:0"}, "CRITERIONS": {"CANDIDATE_CRITERIONS": "crwdns4578:0crwdne4578:0", "RATE_CANDIDATE_BY_CRITERIONS": "crwdns4722:0crwdne4722:0", "TECHNOLOGY_STACK": "crwdns4580:0crwdne4580:0", "ALREADY_EXISTED": "crwdns7186:0crwdne7186:0", "TOASTR_ALREADY_EXIST": "crwdns7188:0crwdne7188:0", "PERSONAL_QUALITIES": "crwdns4582:0crwdne4582:0", "CHOOSE_CRITERIONS": "crwdns4724:0crwdne4724:0", "TECHNOLOGY_PLACEHOLDER": "crwdns4952:0crwdne4952:0", "PERSONAL_QUALITIES_PLACEHOLDER": "crwdns4954:0crwdne4954:0"}}, "ORGANIZATIONS_PAGE": {"ORGANIZATIONS": "crwdns238:0crwdne238:0", "EMPLOYEES": "crwdns240:0crwdne240:0", "POSITIONS": "crwdns7832:0crwdne7832:0", "EDIT_PUBLIC_PAGE": "crwdns3572:0crwdne3572:0", "SELECT_ORGANIZATION": "crwdns8334:0crwdne8334:0", "MAIN": "crwdns244:0crwdne244:0", "TAGS_OPTIONS": "crwdns11233:0crwdne11233:0", "VARIANTS": "crwdns11235:0crwdne11235:0", "DESCRIPTION": "crwdns10519:0crwdne10519:0", "DEPARTMENTS": "crwdns246:0crwdne246:0", "VENDORS": "crwdns252:0crwdne252:0", "VENDOR": "crwdns9588:0crwdne9588:0", "NAME": "crwdns7836:0crwdne7836:0", "EXPENSE_CATEGORIES": "crwdns3310:0crwdne3310:0", "PROJECTS": "crwdns254:0crwdne254:0", "ACTIVE": "crwdns256:0crwdne256:0", "EMPLOYMENT_TYPE": "crwdns9590:0crwdne9590:0", "ARCHIVED": "crwdns258:0crwdne258:0", "LOCATION": "crwdns2263:0crwdne2263:0", "SETTINGS": "crwdns2265:0crwdne2265:0", "REGISTER_AS_EMPLOYEE": "crwdns11509:0crwdne11509:0", "TEAMS": "crwdns7834:0crwdne7834:0", "TEAM_NAME": "crwdns11530:0{{ name }}crwdne11530:0", "NOT_WORKED": "crwdns11532:0crwdne11532:0", "ROLES": "crwdns2647:0crwdne2647:0", "HELP_CENTER": "crwdns7708:0crwdne7708:0", "DOCUMENTS": "crwdns7414:0crwdne7414:0", "DOCUMENTS_NO_DATA_MESSAGE": "crwdns11255:0crwdne11255:0", "EXPENSE_RECURRING": "crwdns7988:0crwdne7988:0", "RECURRING_EXPENSE": "crwdns2844:0crwdne2844:0", "EMPLOYMENT_TYPES": "crwdns3057:0crwdne3057:0", "INVITE_CONTACT": "crwdns5114:0crwdne5114:0", "EMAIL_INVITE": "crwdns2983:0crwdne2983:0", "ADD_LEVEL_OF_EMPLOYEE": "crwdns4486:0crwdne4486:0", "LEVEL_OF_EMPLOYEE": "crwdns4488:0crwdne4488:0", "EMPLOYEE_LEVEL_NO_DATA_MESSAGE": "crwdns11257:0crwdne11257:0", "POSITION_NO_DATA_MESSAGE": "crwdns11259:0crwdne11259:0", "PHONE": "crwdns7838:0crwdne7838:0", "EMAIL": "crwdns7840:0crwdne7840:0", "WEBSITE": "crwdns7842:0crwdne7842:0", "DOCUMENT_URL": "crwdns7846:0crwdne7846:0", "UPDATED": "crwdns7848:0crwdne7848:0", "LEVEL_NAME": "crwdns4492:0crwdne4492:0", "EXPENSE_NAME": "crwdns4494:0crwdne4494:0", "EMPLOYMENT_TYPE_NAME": "crwdns4496:0crwdne4496:0", "EMPLOYMENT_TYPE_NO_DATA_MESSAGE": "crwdns11261:0crwdne11261:0", "VENDORS_NO_DATA_MESSAGE": "crwdns11263:0crwdne11263:0", "EXPENSE_NO_DATA_MESSAGE": "crwdns11265:0crwdne11265:0", "CONTACTS": "crwdns5112:0crwdne5112:0", "LEVEL": "crwdns7956:0crwdne7956:0", "ORGANIZATION": "crwdns9592:0crwdne9592:0", "HOURS_WORKED": "crwdns9594:0crwdne9594:0", "CLIENTS": "crwdns9596:0crwdne9596:0", "PROFILE": "crwdns9598:0crwdne9598:0", "PORTFOLIO": "crwdns9600:0crwdne9600:0", "BROWSE": "crwdns11227:0crwdne11227:0", "SEARCH": "crwdns11229:0crwdne11229:0", "EDIT": {"SETTINGS_SECTION": "crwdns11052:0crwdne11052:0", "ALL": "crwdns11054:0crwdne11054:0", "ACCOUNTING": "crwdns8464:0crwdne8464:0", "HEADER": "crwdns2165:0crwdne2165:0", "CLIENT": "crwdns260:0crwdne260:0", "CONTACT": "crwdns5116:0crwdne5116:0", "NEW_CLIENT": "crwdns262:0crwdne262:0", "NAME": "crwdns264:0crwdne264:0", "PRIMARY_EMAIL": "crwdns266:0crwdne266:0", "PHONE": "crwdns268:0crwdne268:0", "COUNTRY": "crwdns270:0crwdne270:0", "CITY": "crwdns272:0crwdne272:0", "STREET": "crwdns274:0crwdne274:0", "PROJECTS": "crwdns276:0crwdne276:0", "FAX": "crwdns7972:0crwdne7972:0", "FISCAL_INFORMATION": "crwdns7974:0crwdne7974:0", "WEBSITE": "crwdns7976:0crwdne7976:0", "SECOND_ADDRESS": "crwdns7978:0crwdne7978:0", "IMAGE_URL": "crwdns7980:0crwdne7980:0", "POSTCODE": "crwdns7982:0crwdne7982:0", "DEPARTMENT_NAME": "crwdns278:0crwdne278:0", "POSITION_NAME": "crwdns280:0crwdne280:0", "NEW_PROJECT": "crwdns282:0crwdne282:0", "START_DATE": "crwdns284:0crwdne284:0", "END_DATE": "crwdns286:0crwdne286:0", "BILLING": "crwdns6936:0crwdne6936:0", "CURRENCY": "crwdns290:0crwdne290:0", "OWNER": "crwdns6938:0crwdne6938:0", "TEAMS": "crwdns2102:0crwdne2102:0", "ADD_NEW_CONTACT": "crwdns7860:0crwdne7860:0", "EDIT_CONTACT": "crwdns7862:0crwdne7862:0", "GENERAL_SETTINGS": "crwdns2267:0crwdne2267:0", "DESIGN": "crwdns2269:0crwdne2269:0", "BONUS": "crwdns2699:0crwdne2699:0", "INVITE": "crwdns2856:0crwdne2856:0", "CLICK_EMPLOYEE": "crwdns2581:0crwdne2581:0", "EDIT_PROJECT": "crwdns2583:0crwdne2583:0", "REGIONS": "crwdns2635:0crwdne2635:0", "ROLES_PERMISSIONS": "crwdns2649:0crwdne2649:0", "DATE_LIMIT": "crwdns3079:0crwdne3079:0", "USER_ORGANIZATIONS": "crwdns3422:0{{ name }}crwdne3422:0", "ADDED_TO_ORGANIZATION": "crwdns3424:0crwdne3424:0", "USER_WAS_DELETED": "crwdns9856:0{{ name }}crwdne9856:0", "USER_WAS_REMOVED": "crwdns9858:0{{ name }}crwdne9858:0", "EMPLOYEE_POSITION": "crwdns9602:0crwdne9602:0", "PROJECT_URL": "crwdns9604:0crwdne9604:0", "VISIBILITY": "crwdns11221:0crwdne11221:0", "MEMBERS": "crwdns11223:0crwdne11223:0", "SETTINGS": {"TIMER_SETTINGS": "crwdns3254:0crwdne3254:0", "ALLOW_MODIFY_TIME": "crwdns3256:0crwdne3256:0", "ALLOW_MODIFY_TIME_INFO": "crwdns7538:0crwdne7538:0", "ALLOW_DELETE_TIME": "crwdns3548:0crwdne3548:0", "ALLOW_DELETE_TIME_INFO": "crwdns7540:0crwdne7540:0", "ALLOW_MANUAL_TIME": "crwdns3550:0crwdne3550:0", "ALLOW_MANUAL_TIME_INFO": "crwdns7542:0crwdne7542:0", "REQUIRE_REASON": "crwdns3258:0crwdne3258:0", "REQUIRE_REASON_INFO": "crwdns7544:0crwdne7544:0", "REQUIRE_DESCRIPTION": "crwdns3260:0crwdne3260:0", "REQUIRE_DESCRIPTION_INFO": "crwdns10386:0crwdne10386:0", "REQUIRE_PROJECT": "crwdns3262:0crwdne3262:0", "REQUIRE_PROJECT_INFO": "crwdns10388:0crwdne10388:0", "REQUIRE_TASK": "crwdns3264:0crwdne3264:0", "REQUIRE_TASK_INFO": "crwdns10390:0crwdne10390:0", "REQUIRE_CLIENT": "crwdns3266:0crwdne3266:0", "REQUIRE_CLIENT_INFO": "crwdns10392:0crwdne10392:0", "ALLOW_TO_SET_DEFAULT_ORGANIZATION": "crwdns10394:0crwdne10394:0", "INACTIVITY_TIME_LIMIT": "crwdns11493:0crwdne11493:0", "INACTIVITY_TIME_LIMIT_INFO": "crwdns11495:0crwdne11495:0", "ACTIVITY_PROOF_DURATION": "crwdns11497:0crwdne11497:0", "ACTIVITY_PROOF_DURATION_INFO": "crwdns11499:0crwdne11499:0", "ALLOW_TRACK_INACTIVITY": "crwdns11501:0crwdne11501:0"}, "TEAMS_PAGE": {"MANAGERS": "crwdns4764:0crwdne4764:0", "MEMBERS": "crwdns4766:0crwdne4766:0"}}, "PERMISSIONS": {"ADMIN_DASHBOARD_VIEW": "crwdns2651:0crwdne2651:0", "ORG_PAYMENT_VIEW": "crwdns8296:0crwdne8296:0", "ORG_PAYMENT_ADD_EDIT": "crwdns8298:0crwdne8298:0", "ORG_EXPENSES_VIEW": "crwdns2747:0crwdne2747:0", "ORG_EXPENSES_EDIT": "crwdns2655:0crwdne2655:0", "EMPLOYEE_EXPENSES_VIEW": "crwdns8720:0crwdne8720:0", "EMPLOYEE_EXPENSES_EDIT": "crwdns8722:0crwdne8722:0", "ORG_INCOMES_EDIT": "crwdns2657:0crwdne2657:0", "ORG_INCOMES_VIEW": "crwdns2749:0crwdne2749:0", "ORG_PROPOSALS_EDIT": "crwdns7164:0crwdne7164:0", "ORG_PROPOSALS_VIEW": "crwdns2663:0crwdne2663:0", "ORG_PROPOSAL_TEMPLATES_VIEW": "crwdns8724:0crwdne8724:0", "ORG_PROPOSAL_TEMPLATES_EDIT": "crwdns8726:0crwdne8726:0", "ORG_TIME_OFF_VIEW": "crwdns2665:0crwdne2665:0", "ORG_EMPLOYEES_VIEW": "crwdns2667:0crwdne2667:0", "ORG_EMPLOYEES_EDIT": "crwdns2669:0crwdne2669:0", "ORG_CANDIDATES_VIEW": "crwdns3380:0crwdne3380:0", "ORG_CANDIDATES_EDIT": "crwdns3382:0crwdne3382:0", "ORG_USERS_VIEW": "crwdns2671:0crwdne2671:0", "ORG_USERS_EDIT": "crwdns2673:0crwdne2673:0", "ORG_INVITE_VIEW": "crwdns2858:0crwdne2858:0", "ORG_INVITE_EDIT": "crwdns2860:0crwdne2860:0", "ORG_CANDIDATES_DOCUMENTS_VIEW": "crwdns4036:0crwdne4036:0", "ORG_CANDIDATES_TASK_EDIT": "crwdns4038:0crwdne4038:0", "ORG_CANDIDATES_INTERVIEW_EDIT": "crwdns4040:0crwdne4040:0", "ORG_CANDIDATES_INTERVIEW_VIEW": "crwdns11491:0crwdne11491:0", "ORG_INVENTORY_PRODUCT_EDIT": "crwdns4042:0crwdne4042:0", "ORG_TAGS_EDIT": "crwdns4080:0crwdne4080:0", "ORG_CANDIDATES_FEEDBACK_EDIT": "crwdns4082:0crwdne4082:0", "ALL_ORG_VIEW": "crwdns2675:0crwdne2675:0", "ALL_ORG_EDIT": "crwdns2677:0crwdne2677:0", "POLICY_VIEW": "crwdns2701:0crwdne2701:0", "POLICY_EDIT": "crwdns2703:0crwdne2703:0", "CHANGE_SELECTED_EMPLOYEE": "crwdns2739:0crwdne2739:0", "CHANGE_SELECTED_CANDIDATE": "crwdns3384:0crwdne3384:0", "CHANGE_SELECTED_ORGANIZATION": "crwdns2741:0crwdne2741:0", "CHANGE_ROLES_PERMISSIONS": "crwdns2743:0crwdne2743:0", "ACCESS_PRIVATE_PROJECTS": "crwdns2959:0crwdne2959:0", "TIMESHEET_EDIT_TIME": "crwdns3450:0crwdne3450:0", "INVOICES_VIEW": "crwdns3898:0crwdne3898:0", "INVOICES_EDIT": "crwdns7166:0crwdne7166:0", "ESTIMATES_VIEW": "crwdns8728:0crwdne8728:0", "ESTIMATES_EDIT": "crwdns8730:0crwdne8730:0", "EDIT_SALES_PIPELINES": "crwdns4820:0crwdne4820:0", "VIEW_SALES_PIPELINES": "crwdns8752:0crwdne8752:0", "APPROVALS_POLICY_EDIT": "crwdns4774:0crwdne4774:0", "APPROVALS_POLICY_VIEW": "crwdns4776:0crwdne4776:0", "REQUEST_APPROVAL_EDIT": "crwdns4778:0crwdne4778:0", "REQUEST_APPROVAL_VIEW": "crwdns4780:0crwdne4780:0", "ORG_CANDIDATES_INTERVIEWERS_EDIT": "crwdns4782:0crwdne4782:0", "ORG_CANDIDATES_INTERVIEWERS_VIEW": "crwdns11489:0crwdne11489:0", "VIEW_ALL_EMAILS": "crwdns4784:0crwdne4784:0", "VIEW_ALL_EMAIL_TEMPLATES": "crwdns8732:0crwdne8732:0", "ORG_HELP_CENTER_EDIT": "crwdns4786:0crwdne4786:0", "PUBLIC_PAGE_EDIT": "crwdns7938:0crwdne7938:0", "CAN_APPROVE_TIMESHEET": "crwdns4812:0crwdne4812:0", "EVENT_TYPES_VIEW": "crwdns7996:0crwdne7996:0", "TIME_OFF_EDIT": "crwdns7998:0crwdne7998:0", "ORG_INVENTORY_VIEW": "crwdns10288:0crwdne10288:0", "INVENTORY_GALLERY_VIEW": "crwdns10290:0crwdne10290:0", "INVENTORY_GALLERY_EDIT": "crwdns10292:0crwdne10292:0", "ORG_EQUIPMENT_VIEW": "crwdns10294:0crwdne10294:0", "ORG_EQUIPMENT_EDIT": "crwdns10296:0crwdne10296:0", "ORG_EQUIPMENT_SHARING_VIEW": "crwdns10298:0crwdne10298:0", "ORG_EQUIPMENT_SHARING_EDIT": "crwdns10300:0crwdne10300:0", "EQUIPMENT_MAKE_REQUEST": "crwdns10302:0crwdne10302:0", "EQUIPMENT_APPROVE_REQUEST": "crwdns10304:0crwdne10304:0", "ORG_PRODUCT_TYPES_VIEW": "crwdns10306:0crwdne10306:0", "ORG_PRODUCT_TYPES_EDIT": "crwdns10308:0crwdne10308:0", "ORG_PRODUCT_CATEGORIES_VIEW": "crwdns10310:0crwdne10310:0", "ORG_PRODUCT_CATEGORIES_EDIT": "crwdns10478:0crwdne10478:0", "VIEW_ALL_ACCOUNTING_TEMPLATES": "crwdns10314:0crwdne10314:0", "GROUPS": {"GENERAL": "crwdns2679:0crwdne2679:0", "ADMINISTRATION": "crwdns2681:0crwdne2681:0"}, "ONLY_ADMIN": "crwdns2745:0crwdne2745:0", "INSUFFICIENT": "crwdns2751:0crwdne2751:0", "ORG_SPRINT_EDIT": "crwdns7456:0crwdne7456:0", "ORG_SPRINT_VIEW": "crwdns7458:0crwdne7458:0", "ORG_PROJECT_EDIT": "crwdns7460:0crwdne7460:0", "ORG_CONTACT_EDIT": "crwdns7462:0crwdne7462:0", "ORG_CONTACT_VIEW": "crwdns8734:0crwdne8734:0", "ORG_TEAM_EDIT": "crwdns7464:0crwdne7464:0", "ORG_CONTRACT_EDIT": "crwdns7466:0crwdne7466:0", "TIME_TRACKER": "crwdns8224:0crwdne8224:0", "TENANT_ADD_EXISTING_USER": "crwdns8226:0crwdne8226:0", "INTEGRATION_VIEW": "crwdns8736:0crwdne8736:0", "FILE_STORAGE_VIEW": "crwdns8738:0crwdne8738:0", "PAYMENT_GATEWAY_VIEW": "crwdns8740:0crwdne8740:0", "SMS_GATEWAY_VIEW": "crwdns8742:0crwdne8742:0", "CUSTOM_SMTP_VIEW": "crwdns8744:0crwdne8744:0", "IMPORT_EXPORT_VIEW": "crwdns8746:0crwdne8746:0", "ORG_JOB_EMPLOYEE_VIEW": "crwdns8748:0crwdne8748:0", "ORG_JOB_MATCHING_VIEW": "crwdns8750:0crwdne8750:0", "ACCESS_DELETE_ACCOUNT": "crwdns10543:0crwdne10543:0", "ACCESS_DELETE_ALL_DATA": "crwdns10545:0crwdne10545:0", "TENANT_SETTING": "crwdns10859:0crwdne10859:0", "ALLOW_DELETE_TIME": "crwdns10869:0crwdne10869:0", "ALLOW_MODIFY_TIME": "crwdns10871:0crwdne10871:0", "ALLOW_MANUAL_TIME": "crwdns10929:0crwdne10929:0", "DELETE_SCREENSHOTS": "crwdns10954:0crwdne10954:0"}, "BILLING": "crwdns10521:0crwdne10521:0", "BUDGET": "crwdns10523:0crwdne10523:0", "OPEN_SOURCE": "crwdns10525:0crwdne10525:0", "ORGANIZATION_ADD": "crwdns11154:0crwdne11154:0", "IMAGE": "crwdns11211:0crwdne11211:0", "SPRINTS": "crwdns11231:0crwdne11231:0", "NO_IMAGE": "crwdns11237:0crwdne11237:0"}, "CONTACTS_PAGE": {"VISITORS": "crwdns7864:0crwdne7864:0", "LEADS": "crwdns7866:0crwdne7866:0", "CUSTOMERS": "crwdns7868:0crwdne7868:0", "CLIENTS": "crwdns7870:0crwdne7870:0", "CITY": "crwdns7914:0crwdne7914:0", "STREET": "crwdns7916:0crwdne7916:0", "COUNTRY": "crwdns7918:0crwdne7918:0", "PROJECTS": "crwdns7920:0crwdne7920:0", "EMAIL": "crwdns7922:0crwdne7922:0", "PHONE": "crwdns7924:0crwdne7924:0", "CONTACT_TYPE": "crwdns7926:0crwdne7926:0", "MAIN": "crwdns8754:0crwdne8754:0", "ADDRESS": "crwdns8756:0crwdne8756:0", "ADDRESS_2": "crwdns11245:0crwdne11245:0", "MEMBERS": "crwdns8758:0crwdne8758:0", "BUDGET": "crwdns10527:0crwdne10527:0"}, "PUBLIC_PAGE": {"LANGUAGES": "crwdns4798:0crwdne4798:0", "AWARDS": "crwdns4800:0crwdne4800:0", "COMPANY_SKILLS": "crwdns4802:0crwdne4802:0", "SKILLS": "crwdns7746:0crwdne7746:0", "PROFILE": "crwdns11449:0crwdne11449:0", "PORTFOLIO": "crwdns11451:0crwdne11451:0", "OVERVIEW": "crwdns7748:0crwdne7748:0", "DESCRIPTION": "crwdns7750:0crwdne7750:0", "TOTAL_BONUSES_PAID": "crwdns4826:0crwdne4826:0", "COMPANY_PROFILE": "crwdns4828:0crwdne4828:0", "TOTAL_CLIENTS": "crwdns4830:0crwdne4830:0", "YEAR_FOUNDED": "crwdns4832:0crwdne4832:0", "COMPANY_SIZE": "crwdns4834:0crwdne4834:0", "CLIENT_FOCUS": "crwdns4836:0crwdne4836:0", "MONTHLY_INCOME": "crwdns4912:0crwdne4912:0", "TOTAL_INCOME": "crwdns4914:0crwdne4914:0", "TOTAL_PROJECTS": "crwdns4916:0crwdne4916:0", "MINIMUM_PROJECT_SIZE": "crwdns4918:0crwdne4918:0", "EMPLOYEES": "crwdns4920:0crwdne4920:0", "PROFITS": "crwdns4922:0crwdne4922:0", "RATE": "crwdns7804:0crwdne7804:0", "ACTIVE": "crwdns7806:0crwdne7806:0", "STARTED_WORK_ON": "crwdns7808:0crwdne7808:0", "PAY_PERIOD": "crwdns7886:0crwdne7886:0", "AVERAGE_BONUS": "crwdns7888:0crwdne7888:0", "AVERAGE_EXPENSES": "crwdns7890:0crwdne7890:0", "AVERAGE_INCOME": "crwdns7892:0crwdne7892:0", "EMPLOYEE_UPDATED": "crwdns7894:0crwdne7894:0", "IMAGE_UPDATED": "crwdns7896:0crwdne7896:0", "FAIL_TO_UPDATE_IMAGE": "crwdns7898:0crwdne7898:0", "ANONYMOUS": "crwdns7992:0crwdne7992:0"}, "PROPOSALS_PAGE": {"HEADER": "crwdns2151:0crwdne2151:0", "STATISTICS": "crwdns2153:0crwdne2153:0", "ACCEPTED_PROPOSALS": "crwdns2155:0crwdne2155:0", "TOTAL_PROPOSALS": "crwdns2157:0crwdne2157:0", "SUCCESS_RATE": "crwdns2159:0crwdne2159:0", "PROPOSALS": "crwdns9606:0crwdne9606:0", "REGISTER": {"REGISTER_PROPOSALS": "crwdns2219:0crwdne2219:0", "AUTHOR": "crwdns2221:0crwdne2221:0", "TEMPLATE": "crwdns8586:0crwdne8586:0", "JOB_POST_URL": "crwdns2223:0crwdne2223:0", "PICK_A_DATE": "crwdns2225:0crwdne2225:0", "PROPOSAL_DATE": "crwdns7810:0crwdne7810:0", "JOB_POST_CONTENT": "crwdns2227:0crwdne2227:0", "UPLOAD": "crwdns2229:0crwdne2229:0", "PROPOSALS_CONTENT": "crwdns2231:0crwdne2231:0", "REGISTER_PROPOSALS_BUTTON": "crwdns2233:0crwdne2233:0"}, "PROPOSAL_DETAILS": {"PROPOSAL_DETAILS": "crwdns2411:0crwdne2411:0", "EDIT": "crwdns2413:0crwdne2413:0", "AUTHOR": "crwdns2415:0crwdne2415:0", "JOB_POST_URL": "crwdns2417:0crwdne2417:0", "PROPOSAL_SENT_ON": "crwdns2419:0crwdne2419:0", "STATUS": "crwdns2421:0crwdne2421:0", "JOB_POST_CONTENT": "crwdns2423:0crwdne2423:0", "PROPOSAL_CONTENT": "crwdns2425:0crwdne2425:0", "VIEW_JOB_POST": "crwdns10811:0crwdne10811:0"}, "EDIT_PROPOSAL": {"EDIT_PROPOSAL": "crwdns2427:0crwdne2427:0", "AUTHOR": "crwdns2429:0crwdne2429:0", "JOB_POST_URL": "crwdns2431:0crwdne2431:0", "PROPOSAL_SENT_ON": "crwdns2433:0crwdne2433:0", "JOB_POST_CONTENT": "crwdns2435:0crwdne2435:0", "PROPOSAL_CONTENT": "crwdns2437:0crwdne2437:0", "EDIT_PROPOSAL_BUTTON": "crwdns2439:0crwdne2439:0", "PLACEHOLDER": {"JOB_POST_URL": "crwdns2441:0crwdne2441:0"}}}, "APPROVAL_REQUEST_PAGE": {"APPROVAL_REQUEST_NAME": "crwdns4392:0crwdne4392:0", "APPROVAL_REQUEST_TYPE": "crwdns4394:0crwdne4394:0", "APPROVAL_REQUEST_MIN_COUNT": "crwdns4396:0crwdne4396:0", "APPROVAL_REQUEST_APPROVAL_POLICY": "crwdns4398:0crwdne4398:0", "APPROVAL_REQUEST_STATUS": "crwdns4400:0crwdne4400:0", "APPROVAL_REQUEST_ACTIONS": "crwdns7246:0crwdne7246:0", "CREATED_BY": "crwdns7470:0crwdne7470:0", "APPROVE": "crwdns7802:0crwdne7802:0", "REFUSE": "crwdns7250:0crwdne7250:0", "HEADER": "crwdns4402:0crwdne4402:0", "EMPLOYEES": "crwdns7026:0crwdne7026:0", "TEAMS": "crwdns7028:0crwdne7028:0", "APPROVAL_POLICY": "crwdns4404:0crwdne4404:0", "CHOOSE_POLICIES": "crwdns4756:0crwdne4756:0", "EDIT_APPROVAL_REQUEST": "crwdns4842:0crwdne4842:0", "ADD_APPROVAL_REQUEST": "crwdns4844:0crwdne4844:0", "APPROVAL_REQUEST_CREATED": "crwdns8784:0{{ name }}crwdne8784:0", "APPROVAL_REQUEST_UPDATED": "crwdns8786:0{{ name }}crwdne8786:0", "APPROVAL_REQUEST_DELETED": "crwdns8788:0{{ name }}crwdne8788:0", "APPROVAL_SUCCESS": "crwdns8790:0{{ name }}crwdne8790:0", "REFUSE_SUCCESS": "crwdns8792:0{{ name }}crwdne8792:0", "APPROVED": "crwdns9608:0crwdne9608:0", "REFUSED": "crwdns9610:0crwdne9610:0", "REQUESTED": "crwdns9612:0crwdne9612:0", "ACTION": "crwdns9614:0crwdne9614:0", "CREATED_AT": "crwdns11128:0crwdne11128:0"}, "APPROVAL_POLICY_PAGE": {"EDIT_APPROVAL_POLICY": "crwdns4846:0crwdne4846:0", "ADD_APPROVAL_POLICY": "crwdns4848:0crwdne4848:0", "HEADER": "crwdns4850:0crwdne4850:0", "APPROVAL_POLICY_NAME": "crwdns4852:0crwdne4852:0", "APPROVAL_POLICY_TYPE": "crwdns4854:0crwdne4854:0", "APPROVAL_POLICY_DESCRIPTION": "crwdns4856:0crwdne4856:0", "BUSINESS_TRIP": "crwdns9616:0crwdne9616:0", "EQUIPMENT_SHARING": "crwdns9618:0crwdne9618:0", "TIME_OFF": "crwdns9620:0crwdne9620:0"}, "TIME_OFF_PAGE": {"HEADER": "crwdns2497:0crwdne2497:0", "REQUEST": "crwdns7718:0crwdne7718:0", "REQUEST_TIME_OFF": "crwdns7720:0crwdne7720:0", "EDIT": "crwdns7784:0crwdne7784:0", "ADD_HOLIDAYS": "crwdns2501:0crwdne2501:0", "DISPLAY_HOLIDAYS": "crwdns2503:0crwdne2503:0", "HOLIDAY_NAME": "crwdns7302:0crwdne7302:0", "SELECT_EMPLOYEES": "crwdns7304:0crwdne7304:0", "SETTINGS": "crwdns7786:0crwdne7786:0", "EDI": "crwdns7788:0crwdne7788:0", "SELECT_HOLIDAY_NAME": "crwdns7306:0crwdne7306:0", "ADD_OR_REMOVE_EMPLOYEES": "crwdns7308:0crwdne7308:0", "SELECT_TIME_OFF_POLICY": "crwdns8416:0crwdne8416:0", "ADD_A_DESCRIPTION": "crwdns7312:0crwdne7312:0", "DESCRIPTION": "crwdns8686:0crwdne8686:0", "START_DATE": "crwdns8688:0crwdne8688:0", "END_DATE": "crwdns8690:0crwdne8690:0", "REQUEST_DATE": "crwdns8692:0crwdne8692:0", "STATUS": "crwdns8694:0crwdne8694:0", "TIME_OFF_REQUEST": "crwdns9622:0crwdne9622:0", "VIEW_REQUEST_DOCUMENT": "crwdns9624:0crwdne9624:0", "MULTIPLE_EMPLOYEES": "crwdns9626:0crwdne9626:0", "UPLOAD_REQUEST_DOCUMENT": "crwdns9952:0crwdne9952:0", "STATUSES": {"REQUESTED": "crwdns2505:0crwdne2505:0", "APPROVED": "crwdns2507:0crwdne2507:0", "DENIED": "crwdns2509:0crwdne2509:0", "ALL": "crwdns2511:0crwdne2511:0"}, "ACTIONS": {"EDIT": "crwdns7790:0crwdne7790:0", "APPROVE_DAYS_OFF_REQUEST": "crwdns7792:0crwdne7792:0", "DENY_DAYS_OFF_REQUEST": "crwdns7794:0crwdne7794:0", "DELETE_DAYS_OFF_REQUEST": "crwdns7796:0crwdne7796:0"}, "POLICY": {"HEADER": "crwdns2571:0crwdne2571:0", "POLICY": "crwdns7320:0crwdne7320:0", "ADD_POLICY": "crwdns2573:0crwdne2573:0", "EDIT_POLICY": "crwdns2619:0crwdne2619:0", "NAME": "crwdns2621:0crwdne2621:0", "REQUIRES_APPROVAL": "crwdns2623:0crwdne2623:0", "PAID": "crwdns2625:0crwdne2625:0", "NAME_IS_REQUIRED": "crwdns11134:0crwdne11134:0"}, "NOTIFICATIONS": {"NO_CHANGES": "crwdns7322:0crwdne7322:0", "STATUS_SET_APPROVED": "crwdns7728:0crwdne7728:0", "ERR_SET_STATUS": "crwdns7730:0crwdne7730:0", "APPROVED_NO_CHANGES": "crwdns7732:0crwdne7732:0", "RECORD_CREATED": "crwdns8794:0crwdne8794:0", "REQUEST_DENIED": "crwdns7736:0crwdne7736:0", "DENIED_NO_CHANGES": "crwdns7738:0crwdne7738:0", "REQUEST_DELETED": "crwdns8796:0crwdne8796:0", "ERR_LOAD_RECORDS": "crwdns7338:0crwdne7338:0", "ERR_DELETE_REQUEST": "crwdns7742:0crwdne7742:0", "ERR_CREATE_RECORD": "crwdns7342:0crwdne7342:0", "REQUEST_UPDATED": "crwdns7798:0crwdne7798:0", "ERR_UPDATE_RECORD": "crwdns7800:0crwdne7800:0"}}, "TAGS_PAGE": {"HEADER": "crwdns2863:0crwdne2863:0", "ADD_TAGS": "crwdns2921:0crwdne2921:0", "EDIT_TAGS": "crwdns2947:0crwdne2947:0", "TAGS_NAME": "crwdns2967:0crwdne2967:0", "TAGS_DESCRIPTION": "crwdns2969:0crwdne2969:0", "TAGS_COLOR": "crwdns2971:0crwdne2971:0", "TAGS_ADD_TAG": "crwdns8798:0{{ name }}crwdne8798:0", "TAGS_EDIT_TAG": "crwdns8800:0{{ name }}crwdne8800:0", "TAGS_DELETE_TAG": "crwdns8802:0{{ name }}crwdne8802:0", "TAGS_SELECT_NAME": "crwdns3162:0crwdne3162:0", "TAGS_SELECT_COLOR": "crwdns3164:0crwdne3164:0", "TAGS_SELECT_DESCRIPTION": "crwdns3166:0crwdne3166:0", "ADD_NEW_TAG": "crwdns4306:0crwdne4306:0", "TENANT_LEVEL": "crwdns4726:0crwdne4726:0", "TAGS_TYPE": "crwdns10274:0crwdne10274:0"}, "SKILLS_PAGE": {"HEADER": "crwdns3920:0crwdne3920:0"}, "LANGUAGE_PAGE": {"HEADER": "crwdns4650:0crwdne4650:0", "ADD_NEW_LANGUAGE": "crwdns4652:0crwdne4652:0"}, "LANGUAGE_LEVELS": {"CONVERSATIONAL": "crwdns4804:0crwdne4804:0", "NATIVE": "crwdns4806:0crwdne4806:0", "FLUENT": "crwdns4808:0crwdne4808:0"}, "EQUIPMENT_PAGE": {"HEADER": "crwdns2997:0crwdne2997:0", "ADD_EQUIPMENT": "crwdns2999:0crwdne2999:0", "EDIT_EQUIPMENT": "crwdns3001:0crwdne3001:0", "EQUIPMENT_NAME": "crwdns3003:0crwdne3003:0", "EQUIPMENT_TYPE": "crwdns3005:0crwdne3005:0", "EQUIPMENT_SN": "crwdns3007:0crwdne3007:0", "EQUIPMENT_MANUFACTURED_YEAR": "crwdns3009:0crwdne3009:0", "EQUIPMENT_INITIAL_COST": "crwdns3011:0crwdne3011:0", "EQUIPMENT_CURRENCY": "crwdns3013:0crwdne3013:0", "EQUIPMENT_MAX_SHARE_PERIOD": "crwdns3015:0crwdne3015:0", "EQUIPMENT_AUTO_APPROVE": "crwdns3017:0crwdne3017:0", "EQUIPMENT_EDITED": "crwdns3019:0crwdne3019:0", "EQUIPMENT_DELETED": "crwdns8804:0{{ name }}crwdne8804:0", "EQUIPMENT_ADDED": "crwdns3023:0crwdne3023:0", "EQUIPMENT_SAVED": "crwdns8806:0{{ name }}crwdne8806:0", "CURRENCY": "crwdns3792:0crwdne3792:0"}, "EQUIPMENT_SHARING_PAGE": {"HEADER": "crwdns3124:0crwdne3124:0", "ADD_EQUIPMENT_REQUEST": "crwdns10577:0crwdne10577:0", "EDIT_EQUIPMENT_REQUEST": "crwdns10579:0crwdne10579:0", "DELETE_EQUIPMENT_REQUEST": "crwdns10581:0crwdne10581:0", "REQUEST": "crwdns3132:0crwdne3132:0", "EQUIPMENT_NAME": "crwdns3134:0crwdne3134:0", "EQUIPMENT_SHARING_POLICY": "crwdns7752:0crwdne7752:0", "SHARE_REQUEST_DATE": "crwdns8418:0crwdne8418:0", "SHARE_START_DATE": "crwdns3138:0crwdne3138:0", "SHARE_END_DATE": "crwdns3140:0crwdne3140:0", "CREATED_BY": "crwdns7474:0crwdne7474:0", "STATUS": "crwdns3142:0crwdne3142:0", "REQUEST_SAVED": "crwdns9054:0crwdne9054:0", "REQUEST_DELETED": "crwdns9056:0crwdne9056:0", "MESSAGES": {"BEFORE_REQUEST_DAY_ERR": "crwdns3168:0crwdne3168:0", "EXCEED_PERIOD_ERR": "crwdns3170:0crwdne3170:0", "BEFORE_START_DATE_ERR": "crwdns3172:0crwdne3172:0", "ITEM_RETURNED_BEFORE_ERR": "crwdns3174:0crwdne3174:0"}, "ACTIONS": "crwdns7404:0crwdne7404:0", "APPROVE": "crwdns7406:0crwdne7406:0", "REFUSE": "crwdns7408:0crwdne7408:0", "APPROVAL_SUCCESS": "crwdns7410:0crwdne7410:0", "REFUSE_SUCCESS": "crwdns7412:0crwdne7412:0", "APPROVED": "crwdns9628:0crwdne9628:0", "REFUSED": "crwdns9630:0crwdne9630:0", "REQUESTED": "crwdns9632:0crwdne9632:0", "ACTION": "crwdns9634:0crwdne9634:0"}, "EQUIPMENT_SHARING_POLICY_PAGE": {"HEADER": "crwdns7754:0crwdne7754:0", "ADD_EQUIPMENT_SHARING_POLICY": "crwdns7756:0crwdne7756:0", "EDIT_EQUIPMENT_SHARING_POLICY": "crwdns7758:0crwdne7758:0", "DELETE_EQUIPMENT_SHARING_POLICY": "crwdns7760:0crwdne7760:0", "REQUEST": "crwdns7762:0crwdne7762:0", "EQUIPMENT_SHARING_POLICY_NAME": "crwdns7764:0crwdne7764:0", "EQUIPMENT_SHARING_POLICY_ORG": "crwdns7766:0crwdne7766:0", "EQUIPMENT_SHARING_POLICY_DESCRIPTION": "crwdns7768:0crwdne7768:0", "REQUEST_SAVED": "crwdns7770:0crwdne7770:0", "REQUEST_DELETED": "crwdns7772:0crwdne7772:0", "ACTIONS": "crwdns7774:0crwdne7774:0", "MESSAGES": {"EQUIPMENT_REQUEST_SAVED": "crwdns9058:0{{ name }}crwdne9058:0", "EQUIPMENT_REQUEST_DELETED": "crwdns9060:0{{ name }}crwdne9060:0"}}, "INVENTORY_PAGE": {"HEADER": "crwdns3590:0crwdne3590:0", "ADD_INVENTORY_ITEM": "crwdns3592:0crwdne3592:0", "EDIT_INVENTORY_ITEM": "crwdns3752:0crwdne3752:0", "INVENTORY_ITEM_DELETED": "crwdns8808:0{{ name }}crwdne8808:0", "INVENTORY_ITEM_SAVED": "crwdns8810:0{{ name }}crwdne8810:0", "EDIT_PRODUCT_VARIANT": "crwdns3754:0crwdne3754:0", "PRODUCT_VARIANT_SAVED": "crwdns3864:0crwdne3864:0", "NAME": "crwdns3600:0crwdne3600:0", "ENABLED": "crwdns3602:0crwdne3602:0", "PRODUCT_TYPE": "crwdns3604:0crwdne3604:0", "PRODUCT_CATEGORY": "crwdns3606:0crwdne3606:0", "IS_SUBSCRIPTION": "crwdns3608:0crwdne3608:0", "IS_PURCHASE_AUTOMATICALLY": "crwdns3610:0crwdne3610:0", "CAN_BE_SOLD": "crwdns3612:0crwdne3612:0", "CAN_BE_PURCHASED": "crwdns3614:0crwdne3614:0", "CAN_BE_CHARGED": "crwdns3616:0crwdne3616:0", "CAN_BE_RENTED": "crwdns3618:0crwdne3618:0", "IS_EQUIPMENT": "crwdns3620:0crwdne3620:0", "TRACK_INVENTORY": "crwdns3756:0crwdne3756:0", "ADD_OPTION": "crwdns3758:0crwdne3758:0", "EDIT_OPTION": "crwdns4234:0crwdne4234:0", "INTERNATIONAL_REFERENCE": "crwdns3622:0crwdne3622:0", "CODE": "crwdns3624:0crwdne3624:0", "NOTES": "crwdns3626:0crwdne3626:0", "DESCRIPTION": "crwdns3628:0crwdne3628:0", "UNIT_COST": "crwdns3630:0crwdne3630:0", "UNIT_COST_CURRENCY": "crwdns3632:0crwdne3632:0", "RETAIL_PRICE": "crwdns3634:0crwdne3634:0", "RETAIL_PRICE_CURRENCY": "crwdns3636:0crwdne3636:0", "QUANTITY": "crwdns3638:0crwdne3638:0", "TAXES": "crwdns3640:0crwdne3640:0", "BILLING_INVOICING_POLICY": "crwdns3642:0crwdne3642:0", "PRODUCT_TYPES": "crwdns3902:0crwdne3902:0", "PRODUCT_CATEGORIES": "crwdns3904:0crwdne3904:0", "ORGANIZATION": "crwdns3906:0crwdne3906:0", "EDIT_PRODUCT_TYPE": "crwdns3908:0crwdne3908:0", "ADD_PRODUCT_TYPE": "crwdns3910:0crwdne3910:0", "PRODUCT_TYPE_SAVED": "crwdns8812:0{{ name }}crwdne8812:0", "PRODUCT_TYPE_DELETED": "crwdns8814:0{{ name }}crwdne8814:0", "EDIT_PRODUCT_CATEGORY": "crwdns4236:0crwdne4236:0", "ADD_PRODUCT_CATEGORY": "crwdns4238:0crwdne4238:0", "PRODUCT_CATEGORY_SAVED": "crwdns8816:0{{ name }}crwdne8816:0", "PRODUCT_CATEGORY_DELETED": "crwdns8818:0{{ name }}crwdne8818:0", "IMAGE": "crwdns4244:0crwdne4244:0", "LANGUAGE": "crwdns4246:0crwdne4246:0", "PRODUCT_VARIANT_DELETED": "crwdns4294:0crwdne4294:0", "ICON": "crwdns4330:0crwdne4330:0", "ADD_VARIANT": "crwdns4498:0crwdne4498:0", "EDIT_VARIANT": "crwdns4500:0crwdne4500:0", "NO_OPTIONS_LABEL": "crwdns4502:0crwdne4502:0", "OPTIONS": "crwdns4504:0crwdne4504:0", "SELECT_OR_UPLOAD_IMAGE": "crwdns10044:0crwdne10044:0", "SELECT_IMAGE": "crwdns10046:0crwdne10046:0", "NO_IMAGE_SELECTED": "crwdns10048:0crwdne10048:0", "URL": "crwdns10050:0crwdne10050:0", "DIMENSIONS": "crwdns10052:0crwdne10052:0", "FEATURED_IMAGE_WAS_SAVED": "crwdns10054:0crwdne10054:0", "IMAGE_SAVED": "crwdns10056:0crwdne10056:0", "ADD_GALLERY_IMAGE": "crwdns10070:0crwdne10070:0", "SET_FEATURED_IMAGE": "crwdns10072:0crwdne10072:0", "VIEW_GALLERY": "crwdns10074:0crwdne10074:0", "EDIT_IMAGE": "crwdns10076:0crwdne10076:0", "DELETE_IMAGE": "crwdns10078:0crwdne10078:0", "IMAGE_ASSET_DELETED": "crwdns10086:0crwdne10086:0", "CATEGORY": "crwdns10096:0crwdne10096:0", "TYPE": "crwdns10098:0crwdne10098:0", "VIEW_INVENTORY_ITEM": "crwdns10100:0crwdne10100:0", "TAGS": "crwdns10102:0crwdne10102:0", "PRICE": "crwdns10104:0crwdne10104:0", "SAVE": "crwdns10106:0crwdne10106:0", "CANCEL": "crwdns10108:0crwdne10108:0", "WIDTH": "crwdns10110:0crwdne10110:0", "HEIGHT": "crwdns10112:0crwdne10112:0", "IMAGE_ADDED_TO_GALLERY": "crwdns10114:0crwdne10114:0", "IMAGES_ADDED_TO_GALLERY": "crwdns10116:0crwdne10116:0", "IMAGE_ASSET_UPDATED": "crwdns10118:0crwdne10118:0", "EDIT_IMAGE_ASSET": "crwdns10120:0crwdne10120:0", "WAREHOUSES": "crwdns10194:0crwdne10194:0", "EMAIL": "crwdns10196:0crwdne10196:0", "ACTIVE": "crwdns10198:0crwdne10198:0", "INACTIVE": "crwdns11215:0crwdne11215:0", "LOCATION": "crwdns10200:0crwdne10200:0", "ADDRESS": "crwdns10202:0crwdne10202:0", "CREATE_WAREHOUSE": "crwdns10204:0crwdne10204:0", "EDIT_WAREHOUSE": "crwdns10206:0crwdne10206:0", "WAREHOUSE_CREATED": "crwdns10208:0crwdne10208:0", "COULD_NOT_CREATE_WAREHOUSE": "crwdns10210:0crwdne10210:0", "WAREHOUSE_WAS_CREATED": "crwdns10212:0{{ name }}crwdne10212:0", "WAREHOUSE_WAS_DELETED": "crwdns10214:0{{ name }}crwdne10214:0", "WAREHOUSE_WAS_UPDATED": "crwdns10216:0{{ name }}crwdne10216:0", "CITY": "crwdns10218:0crwdne10218:0", "LOGO": "crwdns10220:0crwdne10220:0", "CONTACT": "crwdns10222:0crwdne10222:0", "COUNTRY": "crwdns10224:0crwdne10224:0", "NEW_OPTION_GROUP": "crwdns10250:0crwdne10250:0", "OPTION_GROUP_NAME": "crwdns10252:0crwdne10252:0", "OPTION_TRANSLATIONS": "crwdns10254:0crwdne10254:0", "ADD_PRODUCTS": "crwdns10258:0crwdne10258:0", "MANAGE_VARIANTS_QUANTITY": "crwdns10260:0crwdne10260:0", "ADD_PRODUCT": "crwdns10330:0crwdne10330:0", "STORES": "crwdns10336:0crwdne10336:0", "ADD_STORE": "crwdns10338:0crwdne10338:0", "EDIT_STORE": "crwdns10340:0crwdne10340:0", "CREATE_STORE": "crwdns10342:0crwdne10342:0", "PHONE": "crwdns10344:0crwdne10344:0", "FAX": "crwdns10346:0crwdne10346:0", "FISCAL_INFORMATION": "crwdns10348:0crwdne10348:0", "WEBSITE": "crwdns10350:0crwdne10350:0", "MERCHANTS": "crwdns10352:0crwdne10352:0", "CREATE_MERCHANT": "crwdns10354:0crwdne10354:0", "DELETE_MERCHANT": "crwdns10356:0crwdne10356:0", "EDIT_MERCHANT": "crwdns10358:0crwdne10358:0", "MERCHANT_CREATED_SUCCESSFULLY": "crwdns10482:0{{ name }}crwdne10482:0", "MERCHANT_DELETED_SUCCESSFULLY": "crwdns10362:0{{ name }}crwdne10362:0", "MERCHANT_UPDATED_SUCCESSFULLY": "crwdns10484:0{{ name }}crwdne10484:0", "THIS_FIELD_IS_REQUIRED": "crwdns10366:0crwdne10366:0", "EMAIL_WRONG_FORMAT": "crwdns10368:0crwdne10368:0", "PHONE_WRONG_FORMAT": "crwdns10370:0crwdne10370:0", "SELECTED": "crwdns10374:0crwdne10374:0", "SUCCESSFULLY_ADDED_PRODUCTS": "crwdns10486:0crwdne10486:0", "MAIN": "crwdns10398:0crwdne10398:0", "INVENTORY": "crwdns10400:0crwdne10400:0", "IMAGE_WAS_DELETED": "crwdns11239:0crwdne11239:0"}, "TASKS_PAGE": {"HEADER": "crwdns3029:0crwdne3029:0", "MY_TASK_HEADER": "crwdns4188:0crwdne4188:0", "TEAM_TASKS_HEADER": "crwdns4206:0crwdne4206:0", "ADD_TASKS": "crwdns3031:0crwdne3031:0", "EDIT_TASKS": "crwdns3033:0crwdne3033:0", "EDIT_TASK": "crwdns8820:0crwdne8820:0", "DELETE_TASK": "crwdns8822:0crwdne8822:0", "TASKS_TITLE": "crwdns3035:0crwdne3035:0", "TASKS_DESCRIPTION": "crwdns3037:0crwdne3037:0", "TASKS_LOADED": "crwdns3039:0crwdne3039:0", "TASK_ADDED": "crwdns8824:0crwdne8824:0", "TASK_UPDATED": "crwdns8826:0crwdne8826:0", "TASK_DELETED": "crwdns8828:0crwdne8828:0", "TASKS_PROJECT": "crwdns3047:0crwdne3047:0", "TASKS_CREATOR": "crwdns7162:0crwdne7162:0", "TASK_MEMBERS": "crwdns4100:0crwdne4100:0", "TASK_ASSIGNED_TO": "crwdns4190:0crwdne4190:0", "TASK_TEAMS": "crwdns4166:0crwdne4166:0", "TASK_ID": "crwdns11142:0crwdne11142:0", "TASK_NUMBER": "crwdns11144:0crwdne11144:0", "DUE_DATE": "crwdns3578:0crwdne3578:0", "ESTIMATE": "crwdns3580:0crwdne3580:0", "ESTIMATE_DAYS": "crwdns3582:0crwdne3582:0", "ESTIMATE_HOURS": "crwdns3584:0crwdne3584:0", "ESTIMATE_MINUTES": "crwdns3586:0crwdne3586:0", "TASKS_STATUS": "crwdns3049:0crwdne3049:0", "TODO": "crwdns9636:0crwdne9636:0", "IN_PROGRESS": "crwdns9638:0crwdne9638:0", "FOR_TESTING": "crwdns9640:0crwdne9640:0", "COMPLETED": "crwdns9642:0crwdne9642:0", "TASK_VIEW_MODE": "crwdns9644:0crwdne9644:0", "PROJECT": "crwdns9646:0crwdne9646:0", "COMPLETE_SPRINT": "crwdns9648:0crwdne9648:0", "DATE_START": "crwdns9650:0crwdne9650:0", "DATE_END": "crwdns9652:0crwdne9652:0", "BACKLOG": "crwdns9654:0crwdne9654:0", "EDIT_SPRINT": "crwdns9656:0crwdne9656:0", "DELETE_SPRINT": "crwdns9658:0crwdne9658:0", "SELECT": "crwdns9660:0crwdne9660:0", "SPRINTS_SETTINGS": "crwdns9956:0crwdne9956:0", "ARE_YOU_SURE": "crwdns10488:0crwdne10488:0", "SETTINGS": "crwdns10372:0crwdne10372:0"}, "JOBS": {"EMPLOYEE": "crwdns8162:0crwdne8162:0", "TITLE": "crwdns8164:0crwdne8164:0", "DESCRIPTION": "crwdns8166:0crwdne8166:0", "CREATED_DATE": "crwdns8168:0crwdne8168:0", "STATUS": "crwdns8170:0crwdne8170:0", "ACTION": "crwdns8172:0crwdne8172:0", "APPLY": "crwdns8174:0crwdne8174:0", "APPLIED": "crwdns8176:0crwdne8176:0", "STATUS_CLOSED": "crwdns8194:0crwdne8194:0", "STATUS_OPEN": "crwdns8196:0crwdne8196:0", "STATUS_APPLIED": "crwdns8198:0crwdne8198:0", "VIEW": "crwdns8178:0crwdne8178:0", "HIDE": "crwdns8180:0crwdne8180:0", "NO_JOBS": "crwdns8186:0crwdne8186:0", "JOB_SEARCH": "crwdns8184:0crwdne8184:0", "LOAD_MORE": "crwdns8188:0crwdne8188:0", "HIDE_ALL_CONFIRM": "crwdns8356:0crwdne8356:0", "ACTIONS": "crwdns9662:0crwdne9662:0", "UPWORK": "crwdns9960:0crwdne9960:0", "WEB": "crwdns9962:0crwdne9962:0", "HOURLY": "crwdns9964:0crwdne9964:0", "FIXED": "crwdns9966:0crwdne9966:0", "FILTER": {"TITLE": "crwdns8284:0crwdne8284:0", "SOURCE": "crwdns8286:0crwdne8286:0", "JOB_TYPE": "crwdns8288:0crwdne8288:0", "JOB_STATUS": "crwdns8290:0crwdne8290:0", "BUDGET": "crwdns8292:0crwdne8292:0", "LESS_THAN": "crwdns8696:0crwdne8696:0"}, "BROWSE": "crwdns11084:0crwdne11084:0", "SEARCH": "crwdns11086:0crwdne11086:0", "HISTORY": "crwdns11088:0crwdne11088:0", "EMPLOYEES": "crwdns11090:0crwdne11090:0", "MATCHINGS": "crwdns11092:0crwdne11092:0", "PROPOSALS_TEMPLATE": "crwdns11094:0crwdne11094:0"}, "JOB_MATCHING": {"CONFIGURE_EMPLOYEES_TO_JOBS_MATCHING": "crwdns8200:0crwdne8200:0", "SOURCE": "crwdns8202:0crwdne8202:0", "PRESET": "crwdns8204:0crwdne8204:0", "KEYWORDS": "crwdns8206:0crwdne8206:0", "CATEGORY": "crwdns8228:0crwdne8228:0", "OCCUPATION": "crwdns8230:0crwdne8230:0", "ADD_NEW_CRITERIONS": "crwdns8212:0crwdne8212:0", "FIX_PRICE": "crwdns8214:0crwdne8214:0", "HOURLY": "crwdns8216:0crwdne8216:0", "SAVE": "crwdns8218:0crwdne8218:0", "DELETE": "crwdns8220:0crwdne8220:0", "CRITERIONS": "crwdns8222:0crwdne8222:0", "DELETE_CRITERION_MESSAGE": "crwdns8232:0crwdne8232:0", "SAVE_PRESET_MESSAGE": "crwdns8234:0crwdne8234:0"}, "JOB_EMPLOYEE": {"EMPLOYEE": "crwdns8342:0crwdne8342:0", "EMPLOYEES": "crwdns8344:0crwdne8344:0", "AVAILABLE_JOBS": "crwdns8346:0crwdne8346:0", "APPLIED_JOBS": "crwdns8348:0crwdne8348:0", "JOB_SEARCH_STATUS": "crwdns8350:0crwdne8350:0", "BROWSE": "crwdns11096:0crwdne11096:0", "SEARCH": "crwdns11098:0crwdne11098:0", "HISTORY": "crwdns11100:0crwdne11100:0"}, "PROPOSAL_TEMPLATE": {"PROPOSAL_TEMPLATE": "crwdns8308:0crwdne8308:0", "EDIT_PROPOSAL_TEMPLATE": "crwdns8310:0crwdne8310:0", "ADD_PROPOSAL_TEMPLATE": "crwdns8312:0crwdne8312:0", "SELECT_PROPOSAL_TEMPLATE": "crwdns8328:0crwdne8328:0", "SELECT_EMPLOYEE": "crwdns8330:0crwdne8330:0", "NAME": "crwdns8314:0crwdne8314:0", "CONTENT": "crwdns8316:0crwdne8316:0", "EMPLOYEE": "crwdns8318:0crwdne8318:0", "DESCRIPTION": "crwdns8320:0crwdne8320:0", "IS_DEFAULT": "crwdns8322:0crwdne8322:0", "CONFIRM_DELETE": "crwdns8324:0crwdne8324:0", "PROPOSAL_CREATE_MESSAGE": "crwdns8830:0{{ name }}crwdne8830:0", "PROPOSAL_EDIT_MESSAGE": "crwdns8832:0{{ name }}crwdne8832:0", "PROPOSAL_DELETE_MESSAGE": "crwdns8834:0{{ name }}crwdne8834:0", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "crwdns8836:0{{ name }}crwdne8836:0", "YES": "crwdns9664:0crwdne9664:0", "NO": "crwdns9666:0crwdne9666:0", "BROWSE": "crwdns11122:0crwdne11122:0", "SEARCH": "crwdns11124:0crwdne11124:0"}, "SPRINTS_PAGE": {"SPRINT_ADDED": "crwdns7424:0crwdne7424:0", "SPRINT_UPDATED": "crwdns7426:0crwdne7426:0", "SPRINT_DELETED": "crwdns7428:0crwdne7428:0", "SPRINT": "crwdns9968:0crwdne9968:0", "ADD_SPRINT_NAME": "crwdns9970:0crwdne9970:0"}, "USERS_PAGE": {"HEADER": "crwdns10276:0crwdne10276:0", "ADD_USER": "crwdns2315:0crwdne2315:0", "ADD_EXISTING_USER": "crwdns3158:0crwdne3158:0", "ADD_EXISTING_ORGANIZATION": "crwdns8336:0crwdne8336:0", "ADD_EXISTING_USER_TOOLTIP": "crwdns10813:0crwdne10813:0", "ROLE": {"SUPER_ADMIN": "crwdns3448:0crwdne3448:0", "ADMIN": "crwdns2317:0crwdne2317:0", "MANAGER": "crwdns2319:0crwdne2319:0", "DATA_ENTRY": "crwdns2321:0crwdne2321:0", "VIEWER": "crwdns2323:0crwdne2323:0", "EMPLOYEE": "crwdns2325:0crwdne2325:0", "CANDIDATE": "crwdns3386:0crwdne3386:0", "ROLE": "crwdns8698:0crwdne8698:0"}, "EDIT_USER": {"HEADER": "crwdns2617:0crwdne2617:0", "EDIT_EXISTING_USER": "crwdns3160:0crwdne3160:0", "MAIN": "crwdns3322:0crwdne3322:0", "USER_ORGANIZATIONS": "crwdns3324:0crwdne3324:0"}, "REMOVE_USER": "crwdns9860:0{{ name }}crwdne9860:0", "ACTIVE": "crwdns11162:0crwdne11162:0", "NOT_STARTED": "crwdns11164:0crwdne11164:0"}, "CONTEXT_MENU": {"TIMER": "crwdns2074:0crwdne2074:0", "ADD_INCOME": "crwdns8598:0crwdne8598:0", "ADD_EXPENSE": "crwdns8600:0crwdne8600:0", "INVOICE": "crwdns2080:0crwdne2080:0", "ESTIMATE": "crwdns8602:0crwdne8602:0", "PAYMENT": "crwdns8604:0crwdne8604:0", "TIME_LOG": "crwdns8606:0crwdne8606:0", "CANDIDATE": "crwdns8608:0crwdne8608:0", "PROPOSAL": "crwdns2082:0crwdne2082:0", "CONTRACT": "crwdns2084:0crwdne2084:0", "TEAM": "crwdns2161:0crwdne2161:0", "TASK": "crwdns2086:0crwdne2086:0", "CLIENT": "crwdns2088:0crwdne2088:0", "CONTACT": "crwdns7168:0crwdne7168:0", "PROJECT": "crwdns2090:0crwdne2090:0", "ADD_EMPLOYEE": "crwdns8610:0crwdne8610:0", "CHAT": "crwdns2094:0crwdne2094:0", "FAQ": "crwdns2096:0crwdne2096:0", "HELP": "crwdns2098:0crwdne2098:0"}, "PROFILE_PAGE": {"FIRST_NAME": "crwdns2235:0crwdne2235:0", "LAST_NAME": "crwdns2237:0crwdne2237:0", "EMAIL": "crwdns2239:0crwdne2239:0", "PASSWORD": "crwdns2241:0crwdne2241:0", "REPEAT_PASSWORD": "crwdns2243:0crwdne2243:0", "ERROR": "crwdns2245:0crwdne2245:0", "SAVE": "crwdns2247:0crwdne2247:0", "VALIDATION": {"EMAIL_REQUIRED": "crwdns10547:0crwdne10547:0", "PASSWORDS_DO_NOT_MATCH": "crwdns10549:0crwdne10549:0"}}, "INVITE_PAGE": {"USER": {"MANAGE": "crwdns10278:0crwdne10278:0", "HEADER": "crwdns2369:0crwdne2369:0", "ACTION": "crwdns2371:0crwdne2371:0"}, "EMPLOYEE": {"MANAGE": "crwdns10280:0crwdne10280:0", "HEADER": "crwdns2375:0crwdne2375:0", "ACTION": "crwdns2377:0crwdne2377:0"}, "CANDIDATE": {"MANAGE": "crwdns10282:0crwdne10282:0", "HEADER": "crwdns3444:0crwdne3444:0", "ACTION": "crwdns3446:0crwdne3446:0"}, "SENT": "crwdns2379:0{{total}}crwdne2379:0", "IGNORED": "crwdns2381:0{{total}}crwdnd2381:0{{ignored}}crwdne2381:0", "STATUS": {"INVITED": "crwdns2383:0crwdne2383:0", "EXPIRED": "crwdns2385:0crwdne2385:0", "ACCEPTED": "crwdns2387:0crwdne2387:0"}, "INVITATION_EXPIRATION_OPTIONS": {"DAY": "crwdns10565:0crwdne10565:0", "WEEK": "crwdns10567:0crwdne10567:0", "TWO_WEEK": "crwdns10569:0crwdne10569:0", "MONTH": "crwdns10571:0crwdne10571:0", "NEVER": "crwdns10573:0crwdne10573:0"}}, "INVOICES_PAGE": {"SENDER": "crwdns11020:0crwdne11020:0", "BROWSE": "crwdns11010:0crwdne11010:0", "COMMENT": "crwdns11004:0crwdne11004:0", "COMMENTS": "crwdns11006:0crwdne11006:0", "HEADER": "crwdns3274:0crwdne3274:0", "INVOICE_NUMBER": "crwdns3276:0crwdne3276:0", "ESTIMATE_NUMBER": "crwdns4420:0crwdne4420:0", "INVOICE_DATE": "crwdns4588:0crwdne4588:0", "ESTIMATE_DATE": "crwdns4422:0crwdne4422:0", "DUE_DATE": "crwdns9668:0crwdne9668:0", "CURRENCY": "crwdns9670:0crwdne9670:0", "DISCOUNT": "crwdns9672:0crwdne9672:0", "CONTACT": "crwdns9674:0crwdne9674:0", "TOTAL_VALUE": "crwdns3278:0crwdne3278:0", "PAID_STATUS": "crwdns3280:0crwdne3280:0", "TAX": "crwdns9676:0crwdne9676:0", "TAX_2": "crwdns7220:0crwdne7220:0", "INVOICE_ACCEPTED": "crwdns8536:0crwdne8536:0", "INVOICE_REJECTED": "crwdns8538:0crwdne8538:0", "INVOICES_ADD_INVOICE": "crwdns3282:0crwdne3282:0", "INVOICES_ADD_ESTIMATE": "crwdns4424:0crwdne4424:0", "INVOICES_EDIT_INVOICE": "crwdns3284:0crwdne3284:0", "INVOICES_EDIT_ESTIMATE": "crwdns4426:0crwdne4426:0", "INVOICES_DUPLICATE_INVOICE": "crwdns3750:0crwdne3750:0", "INVOICES_DUPLICATE_ESTIMATE": "crwdns4428:0crwdne4428:0", "INVOICES_DELETE_INVOICE": "crwdns9678:0crwdne9678:0", "INVOICES_DELETE_ESTIMATE": "crwdns9680:0crwdne9680:0", "INVOICES_SELECT_INVOICE_DATE": "crwdns3288:0crwdne3288:0", "INVOICES_SELECT_DUE_DATE": "crwdns3290:0crwdne3290:0", "INVOICES_SELECT_CURRENCY": "crwdns3292:0crwdne3292:0", "INVOICES_SELECT_DISCOUNT_VALUE": "crwdns3294:0crwdne3294:0", "INVOICES_SELECT_DISCOUNT": "crwdns11012:0crwdne11012:0", "INVOICES_SELECT_PAID": "crwdns3296:0crwdne3296:0", "INVOICES_TAXES": "crwdns7776:0crwdne7776:0", "INVOICES_SELECT_TERMS": "crwdns3300:0crwdne3300:0", "ADD_INVOICE": "crwdns3304:0crwdne3304:0", "ADD_ESTIMATE": "crwdns4432:0crwdne4432:0", "EDIT_INVOICE": "crwdns3306:0crwdne3306:0", "EDIT_ESTIMATE": "crwdns4434:0crwdne4434:0", "VIEW_INVOICE": "crwdns3886:0crwdne3886:0", "VIEW_ESTIMATE": "crwdns4436:0crwdne4436:0", "SELECT_EMPLOYEE": "crwdns3644:0crwdne3644:0", "SELECT_PROJECT": "crwdns3646:0crwdne3646:0", "SELECT_TASK": "crwdns3648:0crwdne3648:0", "SELECT_PRODUCT": "crwdns4152:0crwdne4152:0", "INVALID_DATES": "crwdns3732:0crwdne3732:0", "INVOICE_NUMBER_DUPLICATE": "crwdns4114:0crwdne4114:0", "DISCOUNT_TYPE": "crwdns3772:0crwdne3772:0", "TAX_TYPE": "crwdns3774:0crwdne3774:0", "TAX_VALUE": "crwdns9682:0crwdne9682:0", "PERCENT": "crwdns3776:0crwdne3776:0", "FLAT": "crwdns3778:0crwdne3778:0", "SUBTOTAL": "crwdns3780:0crwdne3780:0", "TOTAL": "crwdns3782:0crwdne3782:0", "NAME": "crwdns3888:0crwdne3888:0", "PAID": "crwdns4154:0crwdne4154:0", "NOT_PAID": "crwdns4156:0crwdne4156:0", "RECEIVED_INVOICES": "crwdns4438:0crwdne4438:0", "RECEIVED_ESTIMATES": "crwdns4440:0crwdne4440:0", "SEND_INVOICE": "crwdns3892:0crwdne3892:0", "SEND_ESTIMATE": "crwdns4442:0crwdne4442:0", "APPLY_TAX": "crwdns7222:0crwdne7222:0", "APPLY_DISCOUNT": "crwdns7224:0crwdne7224:0", "APPLIED": "crwdns7226:0crwdne7226:0", "NOT_APPLIED": "crwdns7228:0crwdne7228:0", "STATUS": "crwdns7374:0crwdne7374:0", "SET_STATUS": "crwdns7932:0crwdne7932:0", "SHOW_COLUMNS": "crwdns7416:0crwdne7416:0", "ITEM": "crwdns7492:0crwdne7492:0", "SETTINGS": "crwdns7678:0crwdne7678:0", "SHOW_HIDE_COLUMNS": "crwdns7680:0crwdne7680:0", "INVOICES_PER_PAGE": "crwdns7934:0crwdne7934:0", "ESTIMATES_PER_PAGE": "crwdns7936:0crwdne7936:0", "APPLY_DISCOUNT_AFTER_TAX": "crwdns8098:0crwdne8098:0", "SELECT_INVOICE_TO_VIEW_HISTORY": "crwdns8700:0crwdne8700:0", "INVOICE_SENT_TO": "crwdns9862:0{{ name }}crwdne9862:0", "ESTIMATE_SENT_TO": "crwdns9864:0{{ name }}crwdne9864:0", "INVOICE": "crwdns9688:0crwdne9688:0", "ESTIMATE": "crwdns9690:0crwdne9690:0", "ACTIONS": "crwdns9692:0crwdne9692:0", "HISTORY": "crwdns9694:0crwdne9694:0", "SEARCH": "crwdns9696:0crwdne9696:0", "NUMBER": "crwdns9698:0crwdne9698:0", "FROM": "crwdns9700:0crwdne9700:0", "TO": "crwdns9702:0crwdne9702:0", "DATE": "crwdns9704:0crwdne9704:0", "YES": "crwdns9706:0crwdne9706:0", "NO": "crwdns9708:0crwdne9708:0", "ALREADY_PAID": "crwdns10038:0crwdne10038:0", "AMOUNT_DUE": "crwdns10040:0crwdne10040:0", "INVOICED_REMAINING_AMOUNT": "crwdns10042:0crwdne10042:0", "INVOICE_TYPE": {"INVOICE_TYPE": "crwdns3650:0crwdne3650:0", "ESTIMATE_TYPE": "crwdns4444:0crwdne4444:0", "GENERATE_INVOICE_ITEMS": "crwdns3652:0crwdne3652:0", "GENERATE_ESTIMATE_ITEMS": "crwdns4446:0crwdne4446:0", "GENERATE_FOR_UNINVOICED_EXPENSES": "crwdns7526:0crwdne7526:0", "BY_EMPLOYEE_HOURS": "crwdns3654:0crwdne3654:0", "BY_PROJECT_HOURS": "crwdns3656:0crwdne3656:0", "BY_TASK_HOURS": "crwdns3658:0crwdne3658:0", "BY_PRODUCTS": "crwdns4158:0crwdne4158:0", "BY_EXPENSES": "crwdns7494:0crwdne7494:0", "DETAILED_ITEMS": "crwdns9710:0crwdne9710:0", "SELECT_INVOICE_TYPE": "crwdns4476:0crwdne4476:0", "SELECT_ESTIMATE_TYPE": "crwdns4956:0crwdne4956:0", "SELECT_PROJECTS": "crwdns4478:0crwdne4478:0", "SELECT_TASKS": "crwdns4480:0crwdne4480:0", "SELECT_PRODUCTS": "crwdns4482:0crwdne4482:0", "SELECT_EXPENSES": "crwdns7496:0crwdne7496:0"}, "INVOICE_ITEM": {"ITEM_NUMBER": "crwdns3456:0crwdne3456:0", "TASK": "crwdns3458:0crwdne3458:0", "NAME": "crwdns3460:0crwdne3460:0", "DESCRIPTION": "crwdns3462:0crwdne3462:0", "PRICE": "crwdns3540:0crwdne3540:0", "QUANTITY": "crwdns3662:0crwdne3662:0", "TOTAL_VALUE": "crwdns3468:0crwdne3468:0", "EMPLOYEE": "crwdns3664:0crwdne3664:0", "HOURLY_RATE": "crwdns3666:0crwdne3666:0", "HOURS_WORKED": "crwdns3668:0crwdne3668:0", "PROJECT": "crwdns3670:0crwdne3670:0", "PRODUCT": "crwdns4160:0crwdne4160:0", "EXPENSE": "crwdns7498:0crwdne7498:0", "NO_ITEMS": "crwdns3740:0crwdne3740:0", "INVALID_ITEM": "crwdns3742:0crwdne3742:0", "EMPLOYEE_VALUE": "crwdns3744:0crwdne3744:0", "PROJECT_VALUE": "crwdns3746:0crwdne3746:0", "TASK_VALUE": "crwdns3748:0crwdne3748:0", "ITEM": "crwdns9712:0crwdne9712:0"}, "SEND": {"CONFIRMATION_INVOICE": "crwdns4450:0crwdne4450:0", "CONFIRMATION_ESTIMATE": "crwdns4452:0crwdne4452:0", "ALREADY_SENT_INVOICE": "crwdns4454:0crwdne4454:0", "ALREADY_SENT_ESTIMATE": "crwdns4456:0crwdne4456:0", "NOT_LINKED": "crwdns4810:0crwdne4810:0", "SENT": "crwdns4838:0crwdne4838:0", "NOT_SENT": "crwdns4840:0crwdne4840:0"}, "VIEW": {"FROM": "crwdns4032:0crwdne4032:0", "TO": "crwdns4034:0crwdne4034:0"}, "DOWNLOAD": {"CONFIRMATION_INVOICE": "crwdns4458:0crwdne4458:0", "CONFIRMATION_ESTIMATE": "crwdns4460:0crwdne4460:0", "INVOICE_DOWNLOAD": "crwdns4380:0crwdne4380:0", "ESTIMATE_DOWNLOAD": "crwdns4462:0crwdne4462:0"}, "EMAIL": {"EMAIL_INVOICE": "crwdns4382:0crwdne4382:0", "EMAIL_ESTIMATE": "crwdns4464:0crwdne4464:0", "EMAIL_SENT": "crwdns4384:0crwdne4384:0"}, "ESTIMATES": {"HEADER": "crwdns4466:0crwdne4466:0", "ESTIMATE_NUMBER": "crwdns4468:0crwdne4468:0", "ESTIMATE_DATE": "crwdns4470:0crwdne4470:0", "ACCEPT": "crwdns4958:0crwdne4958:0", "REJECT": "crwdns4960:0crwdne4960:0", "ACCEPTED": "crwdns4962:0crwdne4962:0", "REJECTED": "crwdns4964:0crwdne4964:0", "ACCEPTED_STATUS": "crwdns4966:0crwdne4966:0", "ESTIMATE_CONVERT": "crwdns7230:0crwdne7230:0", "SELECT_ESTIMATE_TO_VIEW_HISTORY": "crwdns8702:0crwdne8702:0", "ESTIMATE_ACCEPTED": "crwdns9714:0crwdne9714:0", "ESTIMATE_REJECTED": "crwdns10490:0crwdne10490:0", "ERROR": "crwdns10492:0crwdne10492:0", "CONVERTED_TO_INVOICE": "crwdns9972:0crwdne9972:0"}, "PAYMENTS": {"HEADER": "crwdns7210:0crwdne7210:0", "TOTAL_VALUE": "crwdns4592:0crwdne4592:0", "RECORD_PAYMENT": "crwdns4594:0crwdne4594:0", "EDIT_PAYMENT": "crwdns4596:0crwdne4596:0", "DELETE_PAYMENT": "crwdns4598:0crwdne4598:0", "TOTAL_PAID": "crwdns4600:0crwdne4600:0", "PAID": "crwdns4602:0crwdne4602:0", "PAYMENT_DATE": "crwdns4604:0crwdne4604:0", "AMOUNT": "crwdns4606:0crwdne4606:0", "RECORDED_BY": "crwdns4608:0crwdne4608:0", "NOTE": "crwdns4610:0crwdne4610:0", "PAYMENT_ADD": "crwdns9066:0crwdne9066:0", "PAYMENT_EDIT": "crwdns9068:0crwdne9068:0", "PAYMENT_DELETE": "crwdns9070:0crwdne9070:0", "PAYMENT_DOWNLOAD": "crwdns8540:0crwdne8540:0", "STATUS": "crwdns7094:0crwdne7094:0", "ON_TIME": "crwdns7096:0crwdne7096:0", "OVERDUE": "crwdns7098:0crwdne7098:0", "NO_PAYMENTS_RECORDED": "crwdns7100:0crwdne7100:0", "LEFT_TO_PAY": "crwdns7212:0crwdne7212:0", "PAYMENT_METHOD": "crwdns7232:0crwdne7232:0", "SELECT_INVOICE": "crwdns9720:0crwdne9720:0", "PAYMENT_AMOUNT_ADDED": "crwdns9722:0{{ amount }}crwdnd9722:0{{ currency }}crwdne9722:0", "PAYMENT": "crwdns9724:0crwdne9724:0", "BANK_TRANSFER": "crwdns9726:0crwdne9726:0", "CASH": "crwdns9728:0crwdne9728:0", "CHEQUE": "crwdns9730:0crwdne9730:0", "CREDIT_CARD": "crwdns9732:0crwdne9732:0", "DEBIT": "crwdns9734:0crwdne9734:0", "ONLINE": "crwdns9736:0crwdne9736:0", "PAYMENTS_FOR_INVOICE": "crwdns9974:0crwdne9974:0", "RECEIVED_FROM": "crwdns9976:0crwdne9976:0", "RECEIVER": "crwdns9978:0crwdne9978:0", "SEND_RECEIPT": "crwdns10182:0{{ name }}crwdne10182:0", "CONTACT_GREETING": "crwdns10184:0{{ name }}crwdne10184:0", "RECEIPT_FOR": "crwdns10186:0{{ invoiceNumber }}crwdnd10186:0{{ amount }}crwdnd10186:0{{ currency }}crwdne10186:0", "BEST_REGARDS": "crwdns10188:0{{ name }}crwdne10188:0"}, "INTERNAL_NOTE": {"NOTE_SAVED": "crwdns9072:0crwdne9072:0", "ADD_INTERNAL_NOTE": "crwdns8710:0crwdne8710:0", "ADD_NOTE": "crwdns8712:0crwdne8712:0", "NOTE": "crwdns8714:0crwdne8714:0", "INTERNAL_NOTE": "crwdns8716:0crwdne8716:0"}, "STATUSES": {"DRAFT": "crwdns9738:0crwdne9738:0", "SENT": "crwdns9740:0crwdne9740:0", "VIEWED": "crwdns9742:0crwdne9742:0", "FULLY_PAID": "crwdns9744:0crwdne9744:0", "PARTIALLY_PAID": "crwdns9746:0crwdne9746:0", "OVERPAID": "crwdns9748:0crwdne9748:0", "VOID": "crwdns9750:0crwdne9750:0", "ACCEPTED": "crwdns9752:0crwdne9752:0", "REJECTED": "crwdns9754:0crwdne9754:0"}, "ACTION": {"DUPLICATE": "crwdns9756:0crwdne9756:0", "SEND": "crwdns9758:0crwdne9758:0", "CONVERT_TO_INVOICE": "crwdns9760:0crwdne9760:0", "EMAIL": "crwdns9762:0crwdne9762:0", "DELETE": "crwdns9764:0crwdne9764:0", "NOTE": "crwdns9766:0crwdne9766:0", "PAYMENTS": "crwdns9768:0crwdne9768:0"}, "PUBLIC_LINK": {"HEADER": "crwdns10064:0crwdne10064:0", "GENERATE": "crwdns10066:0{{ text }}crwdne10066:0", "ACCESS": "crwdns10068:0{{ text }}crwdne10068:0"}}, "PAYMENTS_PAGE": {"HEADER": "crwdns4688:0crwdne4688:0", "CONTACT": "crwdns7628:0crwdne7628:0", "AMOUNT": "crwdns7104:0crwdne7104:0", "PAYMENT_DATE": "crwdns7106:0crwdne7106:0", "RECORDED_BY": "crwdns7108:0crwdne7108:0", "NOTE": "crwdns7110:0crwdne7110:0", "STATUS": "crwdns7112:0crwdne7112:0", "ON_TIME": "crwdns7114:0crwdne7114:0", "OVERDUE": "crwdns7116:0crwdne7116:0", "PAYMENT_METHOD": "crwdns9770:0crwdne9770:0", "PROJECT": "crwdns9772:0crwdne9772:0", "TAGS": "crwdns9774:0crwdne9774:0"}, "HEADER": {"SELECT_EMPLOYEE": "crwdns2639:0crwdne2639:0", "SELECT_A_DATE": "crwdns2641:0crwdne2641:0", "SELECT_AN_ORGANIZATION": "crwdns2643:0crwdne2643:0", "SELECT_PROJECT": "crwdns8704:0crwdne8704:0"}, "HEADER_TITLE": {"FOR": "crwdns10284:0crwdne10284:0", "FROM": "crwdns10286:0crwdne10286:0"}, "PAGE_NOT_FOUND": {"404_PAGE_NOT_FOUND": "crwdns2249:0crwdne2249:0", "TAKE_ME_HOME": "crwdns2251:0crwdne2251:0", "THE_PAGE_YOU_WERE_LOOKING_FOR_DOESNT_EXIST": "crwdns2253:0crwdne2253:0"}, "HELP_PAGE": {"HELP": "crwdns2255:0crwdne2255:0", "KNOWLEDGE_BASE": "crwdns6960:0crwdne6960:0", "CHOSE_ICON": "crwdns4230:0crwdne4230:0", "CREATED_AT": "crwdns7178:0crwdne7178:0", "WRITTEN_BY": "crwdns6964:0crwdne6964:0", "EMPLOYEES": "crwdns6966:0crwdne6966:0", "ONLY_FOR_EMPLOYEES": "crwdns6968:0crwdne6968:0", "DRAFT": "crwdns6970:0crwdne6970:0", "ADD_ARTICLE": "crwdns6972:0crwdne6972:0", "CHOOSE_ANY_CATEGORY": "crwdns6974:0crwdne6974:0", "ARTICLES": "crwdns6976:0crwdne6976:0", "REMOVE_ARTICLE": "crwdns6978:0crwdne6978:0", "ARE_YOU_SURE": "crwdns6980:0crwdne6980:0", "DESCRIPTION": "crwdns6982:0crwdne6982:0", "ARTICLE_TEXT": "crwdns6984:0crwdne6984:0", "EDIT_ARTICLE": "crwdns6986:0crwdne6986:0", "MANAGE_CATEGORY": "crwdns6988:0crwdne6988:0", "ADD_CATEGORY": "crwdns6990:0crwdne6990:0", "EDIT_BASE": "crwdns10332:0crwdne10332:0", "DELETE_BASE": "crwdns9982:0crwdne9982:0", "LANGUAGE": "crwdns6992:0crwdne6992:0", "PUBLISH_STATUS": "crwdns6994:0crwdne6994:0", "PRIVATE_STATUS": "crwdns6996:0crwdne6996:0", "COLOR": "crwdns6998:0crwdne6998:0", "NAME_CATEGORY": "crwdns7000:0crwdne7000:0", "NAME_ARTICLE": "crwdns7002:0crwdne7002:0", "ADD_BASE": "crwdns7006:0crwdne7006:0", "MANAGE_BASE": "crwdns7008:0crwdne7008:0", "NAME_OF_THE_BASE": "crwdns7010:0crwdne7010:0", "REMOVE_CATEGORY": "crwdns7012:0crwdne7012:0", "REMOVE_BASE": "crwdns7014:0crwdne7014:0", "CLEAR": "crwdns7278:0crwdne7278:0", "SEARCH_BY_NAME": "crwdns7364:0crwdne7364:0", "FILTER_BY_AUTHOR": "crwdns7366:0crwdne7366:0", "CATEGORY_EDIT_ADDED": "crwdns8838:0crwdne8838:0", "CATEGORY_EDIT_UPDATED": "crwdns8840:0crwdne8840:0", "CATEGORY_EDIT_DELETED": "crwdns8842:0crwdne8842:0"}, "PROJECT_MANAGEMENT_PAGE": {"THIS_TAB_WILL_SHOW_PROJECT_MANAGEMENT_CHARTS_AND_AGGREGATED_DATA": "crwdns10591:0crwdne10591:0"}, "SETTINGS_FEATURES": {"INVOICE": "crwdns10593:0crwdne10593:0", "INCOME": "crwdns10595:0crwdne10595:0", "EXPENSE": "crwdns10597:0crwdne10597:0", "PAYMENT": "crwdns10599:0crwdne10599:0", "PROPOSAL": "crwdns10601:0crwdne10601:0", "SALES_PIPELINE": "crwdns10603:0crwdne10603:0", "TASK_DASHBOARD": "crwdns10605:0crwdne10605:0", "JOBS": "crwdns10607:0crwdne10607:0", "EMPLOYEES": "crwdns10609:0crwdne10609:0", "TIME_ACTIVITY": "crwdns10611:0crwdne10611:0", "TIMESHEET": "crwdns10613:0crwdne10613:0", "APPOINTMENT_SCHEDULE": "crwdns10615:0crwdne10615:0", "CANDIDATE": "crwdns10617:0crwdne10617:0", "MANAGE_ORGANIZATION": "crwdns10619:0crwdne10619:0", "PRODUCT_INVENTORY": "crwdns10621:0crwdne10621:0", "PROJECT": "crwdns10623:0crwdne10623:0", "ORGANIZATION_TEAM": "crwdns10625:0crwdne10625:0", "ORGANIZATION_DOCUMENT": "crwdns10627:0crwdne10627:0", "LEAD_CUSTOMER_CLIENT": "crwdns10629:0crwdne10629:0", "GOAL_AND_OBJECTIVE": "crwdns10631:0crwdne10631:0", "ALL_REPORT": "crwdns10633:0crwdne10633:0", "USERS": "crwdns10635:0crwdne10635:0", "ORGANIZATIONS": "crwdns10637:0crwdne10637:0", "APPS_INTEGRATIONS": "crwdns10639:0crwdne10639:0", "EMAIL_HISTORY": "crwdns10641:0crwdne10641:0", "SETTING": "crwdns10643:0crwdne10643:0", "ENTITY_IMPORT_EXPORT": "crwdns10645:0crwdne10645:0", "CUSTOM_SMTP": "crwdns10647:0crwdne10647:0", "ROLES_PERMISSIONS": "crwdns10649:0crwdne10649:0", "TIME_TRACKING": "crwdns10651:0crwdne10651:0", "ESTIMATE": "crwdns10653:0crwdne10653:0", "DASHBOARD": "crwdns10655:0crwdne10655:0"}, "SETTINGS_FEATURES_DESCRIPTION": {"INVOICE": {"MANAGE_INVOICE_CREATE_FIRST_INVOICE": "crwdns10657:0crwdne10657:0"}, "INCOME": {"CREATE_FIRST_INCOME": "crwdns10659:0crwdne10659:0"}, "EXPENSE": {"CREATE_FIRST_EXPENSE": "crwdns10661:0crwdne10661:0"}, "PAYMENT": {"MANAGE_PAYMENT_CREATE_FIRST_PAYMENT": "crwdns10663:0crwdne10663:0"}, "PROPOSAL": {"MANAGE_PROPOSAL_REGISTER_FIRST_PROPOSAL": "crwdns10665:0crwdne10665:0"}, "SALES_PIPELINE": {"CREATE_SALES_PIPELINE": "crwdns10667:0crwdne10667:0"}, "TASK_DASHBOARD": {"TASK_DASHBOARD": "crwdns10669:0crwdne10669:0"}, "JOBS": {"JOB_SEARCH_JOBS_MATCHING": "crwdns10671:0crwdne10671:0"}, "EMPLOYEES": {"MANAGE_EMPLOYEES_ADD_OR_INVITE_EMPLOYEES": "crwdns10673:0crwdne10673:0"}, "TIME_ACTIVITY": {"MANAGE_TIME_ACTIVITY_SCREENSHOTS_APP_VISITED_SITES_ACTIVITIES": "crwdns10675:0crwdne10675:0"}, "TIMESHEET": {"MANAGE_EMPLOYEE_TIMESHEET_DAILY_WEEKLY_CALENDAR_CREATE_FIRST_TIMESHEET": "crwdns10677:0crwdne10677:0"}, "APPOINTMENT_SCHEDULE": {"EMPLOYEE_APPOINTMENT_SCHEDULES_BOOK_PUBLIC_APPOINTMENT": "crwdns10679:0crwdne10679:0"}, "CANDIDATE": {"MANAGE_CANDIDATES_INTERVIEWS_INVITES": "crwdns10681:0crwdne10681:0"}, "MANAGE_ORGANIZATION": {"MANAGE_ORGANIZATION_DETAILS_LOCATION_AND_SETTINGS": "crwdns10683:0crwdne10683:0"}, "PRODUCT_INVENTORY": {"MANAGE_PRODUCT_INVENTORY_CREATE_FIRST_PRODUCT": "crwdns10685:0crwdne10685:0"}, "PROJECT": {"MANAGE_PROJECT_CREATE_FIRST_PROJECT": "crwdns10687:0crwdne10687:0"}, "ORGANIZATION_TEAM": {"MANAGE_ORGANIZATION_TEAM_CREATE_FIRST_TEAM": "crwdns10689:0crwdne10689:0"}, "ORGANIZATION_DOCUMENT": {"MANAGE_ORGANIZATION_DOCUMENT_CREATE_FIRST_DOCUMENT": "crwdns10691:0crwdne10691:0"}, "LEAD_CUSTOMER_CLIENT": {"MANAGE_LEADS_CUSTOMERS_AND_CLIENTS_CREATE_FIRST_CUSTOMER/CLIENTS": "crwdns10693:0crwdne10693:0"}, "GOAL_AND_OBJECTIVE": {"MANAGE_GOALS_AND_OBJECTIVES": "crwdns10695:0crwdne10695:0"}, "ALL_REPORT": {"MANAGE_EXPENSE_WEEKLY_TIME_ACTIVITY_AND_ETC_REPORTS": "crwdns10697:0crwdne10697:0"}, "USERS": {"MANAGE_TENANT_USERS": "crwdns10699:0crwdne10699:0"}, "ORGANIZATIONS": {"MANAGE_TENANT_ORGANIZATIONS": "crwdns10701:0crwdne10701:0"}, "APPS_INTEGRATIONS": {"MANAGE_AVAILABLE_APPS_INTEGRATIONS_LIKE_UPWORK_HUBSTAFF": "crwdns10703:0crwdne10703:0"}, "EMAIL_HISTORY": {"MANAGE_EMAIL_HISTORY": "crwdns10705:0crwdne10705:0"}, "SETTING": {"MANAGE_SETTING": "crwdns10707:0crwdne10707:0"}, "ENTITY_IMPORT_EXPORT": {"MANAGE_ENTITY_IMPORT_AND_EXPORT": "crwdns10709:0crwdne10709:0"}, "CUSTOM_SMTP": {"MANAGE_TENANT_ORGANIZATION_CUSTOM_SMTP": "crwdns10711:0crwdne10711:0"}, "ROLES_PERMISSIONS": {"MANAGE_ROLES_PERMISSIONS": "crwdns10713:0crwdne10713:0"}, "TIME_TRACKING": {"DOWNLOAD_DESKTOP_APP_CREATE_FIRST_TIMESHEET": "crwdns10715:0crwdne10715:0"}, "ESTIMATE": {"MANAGE_ESTIMATE_CREATE_FIRST_ESTIMATE": "crwdns10717:0crwdne10717:0"}, "DASHBOARD": {"GO_TO_DASHBOARD_MANAGE_EMPLOYEE_STATISTICS_TIME_TRACKING_DASHBOARD": "crwdns10719:0crwdne10719:0"}}, "SETTINGS_FEATURES_TEXT": {"INVOICE": {"INVOICE_RECEIVED": "crwdns10721:0crwdne10721:0"}, "INCOME": {"": "crwdns10723:0crwdne10723:0"}, "EXPENSE": {"EMPLOYEE_RECURRING_EXPENSE": "crwdns10725:0crwdne10725:0", "ORGANIZATION_RECURRING_EXPENSES": "crwdns10727:0crwdne10727:0"}, "PAYMENT": {"": "crwdns10729:0crwdne10729:0"}, "PROPOSAL": {"PROPOSAL_TEMPLATE": "crwdns10731:0crwdne10731:0"}, "SALES_PIPELINE": {"SALES_PIPELINE_DEAL": "crwdns10733:0crwdne10733:0"}, "TASK_DASHBOARD": {"TEAM_TASK_DASHBOARD": "crwdns10735:0crwdne10735:0", "MY_TASK_DASHBOARD": "crwdns10737:0crwdne10737:0"}, "JOBS": {"": "crwdns10739:0crwdne10739:0"}, "EMPLOYEES": {"EMPLOYEE_LEVEL": "crwdns10741:0crwdne10741:0", "EMPLOYEE_POSITION": "crwdns10743:0crwdne10743:0", "EMPLOYEE_TIME_OFF": "crwdns10745:0crwdne10745:0", "EMPLOYEE_APPROVAL": "crwdns10747:0crwdne10747:0", "EMPLOYEE_APPROVAL_POLICY": "crwdns10749:0crwdne10749:0"}, "TIME_ACTIVITY": {"": "crwdns10751:0crwdne10751:0"}, "TIMESHEET": {"": "crwdns10753:0crwdne10753:0"}, "APPOINTMENT_SCHEDULE": {"": "crwdns10755:0crwdne10755:0"}, "CANDIDATE": {"MANAGE_INTERVIEW": "crwdns10757:0crwdne10757:0", "MANAGE_INVITE": "crwdns10759:0crwdne10759:0"}, "MANAGE_ORGANIZATION": {"HELP_CENTER": "crwdns10761:0crwdne10761:0", "ORGANIZATION_TAG": "crwdns10763:0crwdne10763:0", "ORGANIZATION_EQUIPMENT": "crwdns10765:0crwdne10765:0", "ORGANIZATION_VENDOR": "crwdns10767:0crwdne10767:0", "ORGANIZATION_DEPARTMENT": "crwdns10769:0crwdne10769:0", "ORGANIZATION_EMPLOYMENT_TYPE": "crwdns10771:0crwdne10771:0"}, "PRODUCT_INVENTORY": {"": "crwdns10773:0crwdne10773:0"}, "PROJECT": {"": "crwdns10775:0crwdne10775:0"}, "ORGANIZATION_TEAM": {"": "crwdns10777:0crwdne10777:0"}, "ORGANIZATION_DOCUMENT": {"": "crwdns10779:0crwdne10779:0"}, "LEAD_CUSTOMER_CLIENT": {"": "crwdns10781:0crwdne10781:0"}, "GOAL_AND_OBJECTIVE": {"GOAL_TIME_FRAME_KPI": "crwdns10783:0crwdne10783:0"}, "ALL_REPORT": {"": "crwdns10785:0crwdne10785:0"}, "USERS": {"": "crwdns10787:0crwdne10787:0"}, "ORGANIZATIONS": {"": "crwdns10789:0crwdne10789:0"}, "APPS_INTEGRATIONS": {"": "crwdns10791:0crwdne10791:0"}, "EMAIL_HISTORY": {"CUSTOM_EMAIL_TEMPLATE": "crwdns10793:0crwdne10793:0"}, "SETTING": {"FILE_STORAGE": "crwdns10795:0crwdne10795:0", "SMS_GATEWAY": "crwdns10797:0crwdne10797:0"}, "ENTITY_IMPORT_EXPORT": {"": "crwdns10799:0crwdne10799:0"}, "CUSTOM_SMTP": {"": "crwdns10801:0crwdne10801:0"}, "ROLES_PERMISSIONS": {"": "crwdns10803:0crwdne10803:0"}, "TIME_TRACKING": {"": "crwdns10805:0crwdne10805:0"}, "ESTIMATE": {"ESTIMATE_RECEIVED": "crwdns10807:0crwdne10807:0"}, "DASHBOARD": {"": "crwdns10809:0crwdne10809:0"}}, "ABOUT_PAGE": {"ABOUT": "crwdns2257:0crwdne2257:0"}, "FOOTER": {"BY": "crwdns292:0crwdne292:0", "RIGHTS_RESERVED": "crwdns8420:0crwdne8420:0", "PRESENT": "crwdns2939:0crwdne2939:0", "TERMS_OF_SERVICE": "crwdns9776:0crwdne9776:0", "PRIVACY_POLICY": "crwdns9778:0crwdne9778:0"}, "TOASTR": {"TITLE": {"SUCCESS": "crwdns2389:0crwdne2389:0", "ERROR": "crwdns2391:0crwdne2391:0", "INFO": "crwdns8844:0crwdne8844:0", "WARNING": "crwdns2393:0crwdne2393:0", "MAX_LIMIT_REACHED": "crwdns7528:0crwdne7528:0"}, "MESSAGE": {"ERRORS": "crwdns7430:0crwdne7430:0", "PROJECT_LOAD": "crwdns2395:0crwdne2395:0", "COPIED": "crwdns2397:0crwdne2397:0", "INVITES_LOAD": "crwdns2443:0crwdne2443:0", "INVITES_RESEND": "crwdns9866:0{{ email }}crwdne9866:0", "INVITES_DELETE": "crwdns9868:0{{ email }}crwdne9868:0", "EMPLOYEE_DEPARTMENT_ADDED": "crwdns2521:0crwdne2521:0", "EMPLOYEE_DEPARTMENT_REMOVED": "crwdns2523:0crwdne2523:0", "EMPLOYEE_PROJECT_ADDED": "crwdns2525:0crwdne2525:0", "EMPLOYEE_PROJECT_REMOVED": "crwdns2527:0crwdne2527:0", "EMPLOYEE_CLIENT_ADDED": "crwdns2529:0crwdne2529:0", "EMPLOYEE_CLIENT_REMOVED": "crwdns2531:0crwdne2531:0", "EMPLOYEE_EDIT_ERROR": "crwdns2533:0crwdne2533:0", "EMPLOYEE_PROFILE_UPDATE": "crwdns8846:0{{name}}crwdne8846:0", "EMPLOYEE_LEVEL_UPDATE": "crwdns8848:0{{ name }}crwdne8848:0", "EMPLOYEE_ADDED": "crwdns8850:0{{name}}crwdnd8850:0{{organization}}crwdne8850:0", "EMPLOYEE_INACTIVE": "crwdns8852:0{{name}}crwdne8852:0", "EMPLOYEE_ACTIVE": "crwdns8854:0{{name}}crwdne8854:0", "EMPLOYEE_JOB_STATUS_ACTIVE": "crwdns11485:0{{name}}crwdne11485:0", "EMPLOYEE_JOB_STATUS_INACTIVE": "crwdns11487:0{{name}}crwdne11487:0", "EMPLOYEE_TIME_TRACKING_ENABLED": "crwdns10944:0{{name}}crwdne10944:0", "EMPLOYEE_TIME_TRACKING_DISABLED": "crwdns10946:0{{name}}crwdne10946:0", "CONFIRM": "crwdns2561:0crwdne2561:0", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "crwdns2563:0crwdne2563:0", "OK": "crwdns2565:0crwdne2565:0", "CANCEL": "crwdns2567:0crwdne2567:0", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "crwdns2569:0crwdne2569:0", "PERMISSION_UPDATED": "crwdns2683:0{{ permissionName }}crwdnd2683:0{{ roleName }}crwdne2683:0", "ROLE_CREATED": "crwdns10861:0{{ name }}crwdne10861:0", "ROLE_DELETED": "crwdns10863:0{{ name }}crwdne10863:0", "ROLE_CREATED_ERROR": "crwdns10865:0{{ name }}crwdne10865:0", "ROLE_DELETED_ERROR": "crwdns10867:0{{ name }}crwdne10867:0", "PERMISSION_UPDATE_ERROR": "crwdns2685:0crwdne2685:0", "REGISTER_PROPOSAL_NO_EMPLOYEE_MSG": "crwdns8422:0crwdne8422:0", "NEW_ORGANIZATION_PROJECT_INVALID_NAME": "crwdns2771:0crwdne2771:0", "NEW_ORGANIZATION_TEAM_INVALID_NAME": "crwdns2773:0crwdne2773:0", "NEW_ORGANIZATION_VENDOR_INVALID_NAME": "crwdns2775:0crwdne2775:0", "NEW_ORGANIZATION_EXPENSE_CATEGORY_INVALID_NAME": "crwdns8122:0crwdne8122:0", "NEW_ORGANIZATION_POSITION_INVALID_NAME": "crwdns2777:0crwdne2777:0", "NEW_ORGANIZATION_EMPLOYEE_LEVEL_INVALID_NAME": "crwdns3484:0crwdne3484:0", "NEW_ORGANIZATION_INVALID_EMPLOYMENT_TYPE": "crwdns3061:0crwdne3061:0", "NEW_ORGANIZATION_DEPARTMENT_INVALID_NAME": "crwdns2779:0crwdne2779:0", "NEW_ORGANIZATION_CLIENT_INVALID_DATA": "crwdns2781:0crwdne2781:0", "NEW_ORGANIZATION_AWARD_INVALID_NAME": "crwdns4654:0crwdne4654:0", "NEW_ORGANIZATION_LANGUAGE_INVALID_NAME": "crwdns4656:0crwdne4656:0", "MAIN_ORGANIZATION_UPDATED": "crwdns9078:0{{ name }}crwdne9078:0", "CANDIDATE_SKILL_REQUIRED": "crwdns3714:0crwdne3714:0", "CANDIDATE_EDIT_CREATED": "crwdns9082:0crwdne9082:0", "CANDIDATE_EDIT_UPDATED": "crwdns3876:0crwdne3876:0", "CANDIDATE_EDIT_DELETED": "crwdns3878:0crwdne3878:0", "CANDIDATE_EDUCATION_REQUIRED": "crwdns3716:0crwdne3716:0", "CANDIDATE_FEEDBACK_REQUIRED": "crwdns3880:0crwdne3880:0", "CANDIDATE_FEEDBACK_ABILITY": "crwdns7524:0crwdne7524:0", "CANDIDATE_EXPERIENCE_REQUIRED": "crwdns3718:0crwdne3718:0", "CANDIDATE_DOCUMENT_REQUIRED": "crwdns3836:0crwdne3836:0", "CANDIDATE_SKILLS_REQUIRED": "crwdns3766:0crwdne3766:0", "CANDIDATE_PROFILE_UPDATE": "crwdns9084:0{{name}}crwdne9084:0", "NAME_REQUIRED": "crwdns2865:0crwdne2865:0", "CONTACT_TYPE_REQUIRED": "crwdns7036:0crwdne7036:0", "EMAIL_REQUIRED": "crwdns2867:0crwdne2867:0", "EMAIL_SHOULD_BE_REAL": "crwdns2869:0crwdne2869:0", "PHONE_REQUIRED": "crwdns2871:0crwdne2871:0", "PHONE_CONTAINS_ONLY_NUMBERS": "crwdns2873:0crwdne2873:0", "SOMETHING_BAD_HAPPENED": "crwdns4296:0crwdne4296:0", "EMAIL_TEMPLATE_SAVED": "crwdns9086:0{{ templateName }}crwdne9086:0", "DELETED": "crwdns4670:0crwdne4670:0", "CREATED": "crwdns4908:0crwdne4908:0", "UPDATED": "crwdns4910:0crwdne4910:0", "MOVED_BASE": "crwdns8858:0crwdne8858:0", "MOVED_CATEGORY": "crwdns8860:0crwdne8860:0", "CREATED_BASE": "crwdns8862:0{{ name }}crwdne8862:0", "EDITED_BASE": "crwdns8864:0{{ name }}crwdne8864:0", "DELETED_BASE": "crwdns8866:0{{ name }}crwdne8866:0", "DELETED_CATEGORY": "crwdns8868:0{{ name }}crwdne8868:0", "CREATED_CATEGORY": "crwdns8870:0crwdne8870:0", "EDIT_ADD_CATEGORY": "crwdns8872:0{{ name }}crwdne8872:0", "EDITED_CATEGORY": "crwdns8874:0{{ name }}crwdne8874:0", "HELP_ARTICLE_CREATED": "crwdns10494:0crwdne10494:0", "HELP_ARTICLE_UPDATED": "crwdns10496:0{{ name }}crwdne10496:0", "HELP_ARTICLE_DELETED": "crwdns10498:0{{ name }}crwdne10498:0", "PIPELINE_CREATED": "crwdns9088:0{{ name }}crwdne9088:0", "PIPELINE_UPDATED": "crwdns9090:0{{ name }}crwdne9090:0", "PIPELINE_DELETED": "crwdns9092:0{{ name }}crwdne9092:0", "OBJECTIVE_ADDED": "crwdns8886:0crwdne8886:0", "OBJECTIVE_DELETED": "crwdns8888:0crwdne8888:0", "OBJECTIVE_UPDATED": "crwdns8890:0crwdne8890:0", "KEY_RESULT_ADDED": "crwdns8892:0crwdne8892:0", "KEY_RESULT_DELETED": "crwdns8894:0crwdne8894:0", "KEY_RESULT_UPDATED": "crwdns8896:0crwdne8896:0", "TIME_FRAME_CREATED": "crwdns8898:0{{ name }}crwdne8898:0", "TIME_FRAME_UPDATED": "crwdns8900:0{{ name }}crwdne8900:0", "TIME_FRAME_DELETED": "crwdns8902:0{{ name }}crwdne8902:0", "KPI_CREATED": "crwdns8904:0crwdne8904:0", "KPI_UPDATED": "crwdns8906:0crwdne8906:0", "KPI_DELETED": "crwdns8908:0crwdne8908:0", "EDIT_PAST_INTERVIEW": "crwdns8000:0crwdne8000:0", "ARCHIVE_INTERVIEW": "crwdns8038:0crwdne8038:0", "DELETE_PAST_INTERVIEW": "crwdns8002:0crwdne8002:0", "GOAL_GENERAL_SETTING_UPDATED": "crwdns7522:0crwdne7522:0", "MAX_OBJECTIVE_LIMIT": "crwdns7530:0crwdne7530:0", "MAX_KEY_RESULT_LIMIT": "crwdns7532:0crwdne7532:0", "CUSTOM_SMTP_ADDED": "crwdns8386:0crwdne8386:0", "CUSTOM_SMTP_UPDATED": "crwdns8388:0crwdne8388:0", "JOB_MATCHING_SAVED": "crwdns8910:0crwdne8910:0", "JOB_MATCHING_ERROR": "crwdns8912:0crwdne8912:0", "JOB_MATCHING_DELETED": "crwdns8914:0crwdne8914:0", "APPROVAL_POLICY_CREATED": "crwdns8916:0{{ name }}crwdne8916:0", "APPROVAL_POLICY_UPDATED": "crwdns8918:0{{ name }}crwdne8918:0", "APPROVAL_POLICY_DELETED": "crwdns8920:0{{ name }}crwdne8920:0", "CANDIDATE_CREATED": "crwdns8922:0{{ name }}crwdnd8922:0{{ organization }}crwdne8922:0", "CANDIDATE_ARCHIVED": "crwdns8924:0{{ name }}crwdne8924:0", "CANDIDATE_REJECTED": "crwdns8926:0{{ name }}crwdne8926:0", "CANDIDATE_HIRED": "crwdns8928:0{{ name }}crwdne8928:0", "CANDIDATE_DELETED": "crwdns8930:0{{ name }}crwdne8930:0", "PRESET_SAVED": "crwdns9784:0crwdne9784:0", "JOB_APPLIED": "crwdns9786:0crwdne9786:0", "JOB_HIDDEN": "crwdns9788:0crwdne9788:0", "ORGANIZATION_LOCATION_UPDATED": "crwdns9870:0{{ name }}crwdne9870:0", "ORGANIZATION_INFO_UPDATED": "crwdns9872:0{{ name }}crwdne9872:0", "ORGANIZATION_SETTINGS_UPDATED": "crwdns9874:0{{ name }}crwdne9874:0", "SETTINGS_SAVED": "crwdns10500:0crwdne10500:0", "KEY_RESULTS_CREATED": "crwdns9794:0crwdne9794:0", "INVITE_EMAIL_DELETED": "crwdns9796:0{{ name }}crwdne9796:0", "HOLIDAY_ERROR": "crwdns9094:0crwdne9094:0", "INTERVAL_ERROR": "crwdns9096:0crwdne9096:0", "PROFILE_UPDATED": "crwdns9098:0crwdne9098:0", "PERSONAL_QUALITIES_CREATED": "crwdns9100:0{{ name }}crwdne9100:0", "PERSONAL_QUALITIES_UPDATED": "crwdns9102:0{{ name }}crwdne9102:0", "PERSONAL_QUALITIES_DELETED": "crwdns9104:0{{ name }}crwdne9104:0", "TECHNOLOGY_STACK_CREATED": "crwdns9106:0{{ name }}crwdne9106:0", "TECHNOLOGY_STACK_UPDATED": "crwdns9108:0{{ name }}crwdne9108:0", "TECHNOLOGY_STACK_DELETED": "crwdns9110:0{{ name }}crwdne9110:0", "ARCHIVE_INTERVIEW_SET": "crwdns9112:0{{ name }}crwdne9112:0", "INTERVIEW_UPDATED": "crwdns9114:0{{ name }}crwdne9114:0", "INTERVIEW_DELETED": "crwdns9116:0{{ name }}crwdne9116:0", "INTERVIEW_FEEDBACK_CREATED": "crwdns9118:0{{ name }}crwdne9118:0", "CANDIDATE_EDUCATION_CREATED": "crwdns9120:0{{ name }}crwdne9120:0", "CANDIDATE_EDUCATION_UPDATED": "crwdns9122:0{{ name }}crwdne9122:0", "CANDIDATE_EDUCATION_DELETED": "crwdns9124:0{{ name }}crwdne9124:0", "CANDIDATE_EXPERIENCE_CREATED": "crwdns9126:0{{ name }}crwdne9126:0", "CANDIDATE_EXPERIENCE_UPDATED": "crwdns9128:0{{ name }}crwdne9128:0", "CANDIDATE_EXPERIENCE_DELETED": "crwdns9130:0{{ name }}crwdne9130:0", "CANDIDATE_SKILL_CREATED": "crwdns9132:0{{ name }}crwdne9132:0", "CANDIDATE_SKILL_UPDATED": "crwdns9134:0{{ name }}crwdne9134:0", "CANDIDATE_SKILL_DELETED": "crwdns9136:0{{ name }}crwdne9136:0", "CANDIDATE_DOCUMENT_CREATED": "crwdns9138:0{{ name }}crwdne9138:0", "CANDIDATE_DOCUMENT_UPDATED": "crwdns9140:0{{ name }}crwdne9140:0", "CANDIDATE_DOCUMENT_DELETED": "crwdns9142:0{{ name }}crwdne9142:0", "CANDIDATE_INTERVIEW_CREATED": "crwdns9144:0{{ name }}crwdne9144:0", "CANDIDATE_INTERVIEW_UPDATED": "crwdns9146:0{{ name }}crwdne9146:0", "CANDIDATE_INTERVIEW_DELETED": "crwdns9148:0{{ name }}crwdne9148:0", "CANDIDATE_FEEDBACK_CREATED": "crwdns9150:0crwdne9150:0", "CANDIDATE_FEEDBACK_UPDATED": "crwdns9152:0crwdne9152:0", "CANDIDATE_FEEDBACK_DELETED": "crwdns9154:0crwdne9154:0", "RECURRING_EXPENSE_SET": "crwdns9156:0{{ name }}crwdne9156:0", "RECURRING_EXPENSE_UPDATED": "crwdns9158:0{{ name }}crwdne9158:0", "RECURRING_EXPENSE_DELETED": "crwdns9160:0{{ name }}crwdne9160:0", "IMAGE_UPDATED": "crwdns9162:0crwdne9162:0", "ORGANIZATION_PAGE_UPDATED": "crwdns9164:0{{ name }}crwdne9164:0", "SCREENSHOT_DELETED": "crwdns10883:0{{ name }}crwdnd10883:0{{ organization }}crwdne10883:0", "TIME_LOG_DELETED": "crwdns11014:0{{ name }}crwdnd11014:0{{ organization }}crwdne11014:0", "TIME_LOGS_DELETED": "crwdns11016:0{{ organization }}crwdne11016:0", "BUCKET_CREATED": "crwdns10996:0{{ bucket }}crwdnd10996:0{{ region }}crwdne10996:0", "AUTHORIZED_TO_WORK": "crwdns11529:0{{ name }}crwdne11529:0"}}, "ACCEPT_INVITE": {"ACCEPT_INVITE_FORM": {"FULL_NAME": "crwdns2603:0crwdne2603:0", "ENTER_YOUR_FULL_NAME": "crwdns2605:0crwdne2605:0", "PASSWORD": "crwdns2607:0crwdne2607:0", "REPEAT_PASSWORD": "crwdns2609:0crwdne2609:0", "AGREE_TO": "crwdns2611:0crwdne2611:0", "TERMS_AND_CONDITIONS": "crwdns2613:0crwdne2613:0", "ADD_ORGANIZATION": "crwdns4748:0crwdne4748:0", "COMPLETE_REGISTRATION": "crwdns2615:0crwdne2615:0", "PASSWORDS_DO_NOT_MATCH": "crwdns9798:0crwdne9798:0"}, "INVALID_INVITE": "crwdns4750:0crwdne4750:0", "HEADING": "crwdns4752:0{{ organizationName }}crwdne4752:0", "SUB_HEADING": "crwdns4754:0{{ email }}crwdne4754:0", "INVITATION_NO_LONGER_VALID": "crwdns9800:0crwdne9800:0", "ACCOUNT_CREATED": "crwdns9802:0crwdne9802:0", "COULD_NOT_CREATE_ACCOUNT": "crwdns9804:0crwdne9804:0"}, "NOTES": {"INCOME": {"ADD_INCOME": "crwdns9166:0{{ name }}crwdne9166:0", "EDIT_INCOME": "crwdns9168:0{{ name }}crwdne9168:0", "DELETE_INCOME": "crwdns9170:0{{ name }}crwdne9170:0", "INCOME_ERROR": "crwdns2711:0{{ error }}crwdne2711:0"}, "INVOICE": {"ADD_INVOICE": "crwdns9876:0{{ name }}crwdne9876:0", "EDIT_INVOICE": "crwdns9878:0{{ name }}crwdne9878:0", "DELETE_INVOICE": "crwdns9880:0{{ name }}crwdne9880:0", "INVOICE_ERROR": "crwdns7636:0{{ error }}crwdne7636:0"}, "EXPENSES": {"ADD_EXPENSE": "crwdns9882:0{{ name }}crwdne9882:0", "OPEN_EDIT_EXPENSE_DIALOG": "crwdns9884:0{{ name }}crwdne9884:0", "DELETE_EXPENSE": "crwdns9886:0{{ name }}crwdne9886:0", "EXPENSES_ERROR": "crwdns2721:0{{ error }}crwdne2721:0"}, "PROPOSALS": {"EDIT_PROPOSAL": "crwdns8424:0crwdne8424:0", "REGISTER_PROPOSAL": "crwdns9172:0crwdne9172:0", "REGISTER_PROPOSAL_NO_EMPLOYEE_SELECTED": "crwdns8426:0crwdne8426:0", "REGISTER_PROPOSAL_ERROR": "crwdns2729:0{{ error }}crwdne2729:0", "DELETE_PROPOSAL": "crwdns9174:0crwdne9174:0", "PROPOSAL_ACCEPTED": "crwdns2785:0crwdne2785:0", "PROPOSAL_SENT": "crwdns2787:0crwdne2787:0"}, "POLICY": {"ADD_POLICY": "crwdns8932:0{{ name }}crwdne8932:0", "EDIT_POLICY": "crwdns8934:0{{ name }}crwdne8934:0", "DELETE_POLICY": "crwdns8936:0{{ name }}crwdne8936:0", "ERROR": "crwdns2789:0{{ error }}crwdne2789:0", "SAVE_ERROR": "crwdns8938:0crwdne8938:0"}, "USER": {"EDIT_PROFILE": "crwdns2737:0crwdne2737:0"}, "CANDIDATE": {"INVALID_FORM": "crwdns3844:0crwdne3844:0", "INVALID_FEEDBACK_INFO": "crwdns4358:0crwdne4358:0", "EXPERIENCE": {"INVALID_CANDIDATE_NAME": "crwdns3720:0crwdne3720:0", "INVALID_FORM": "crwdns3722:0crwdne3722:0", "INVALID_FIELD": "crwdns3768:0crwdne3768:0", "ERROR": "crwdns3770:0{{ error }}crwdne3770:0"}}, "EMPLOYEE": {"EDIT_EMPLOYEE_AWARDS": {"ADD_AWARD": "crwdns9176:0{{ name }}crwdne9176:0", "INVALID_AWARD_NAME_YEAR": "crwdns8006:0crwdne8006:0", "REMOVE_AWARD": "crwdns9178:0{{ name }}crwdne9178:0"}}, "ORGANIZATIONS": {"ADD_NEW_ORGANIZATION": "crwdns9888:0{{ name }}crwdne9888:0", "DELETE_ORGANIZATION": "crwdns9890:0{{ name }}crwdne9890:0", "ADD_NEW_USER_TO_ORGANIZATION": "crwdns9892:0{{ username }}crwdnd9892:0{{ orgname }}crwdne9892:0", "DELETE_USER_FROM_ORGANIZATION": "crwdns9894:0{{ username }}crwdne9894:0", "DATA_ERROR": "crwdns2799:0{{ error }}crwdne2799:0", "EDIT_ORGANIZATIONS_PROJECTS": {"ADD_PROJECT": "crwdns8940:0{{ name }}crwdne8940:0", "REMOVE_PROJECT": "crwdns8942:0{{ name }}crwdne8942:0", "INVALID_PROJECT_NAME": "crwdns2805:0crwdne2805:0", "VISIBILITY": "crwdns11225:0{{ name }}crwdne11225:0"}, "EDIT_ORGANIZATIONS_TEAM": {"ADD_NEW_TEAM": "crwdns8944:0{{ name }}crwdne8944:0", "EDIT_EXISTING_TEAM": "crwdns8946:0{{ name }}crwdne8946:0", "INVALID_TEAM_NAME": "crwdns2811:0crwdne2811:0", "REMOVE_TEAM": "crwdns8948:0{{ name }}crwdne8948:0"}, "EDIT_ORGANIZATIONS_EMPLOYEE_LEVELS": {"ADD_EMPLOYEE_LEVEL": "crwdns8950:0{{ name }}crwdne8950:0", "REMOVE_EMPLOYEE_LEVEL": "crwdns8952:0{{ name }}crwdne8952:0", "INVALID_EMPLOYEE_LEVEL": "crwdns3490:0crwdne3490:0"}, "EDIT_ORGANIZATIONS_VENDOR": {"ADD_VENDOR": "crwdns8954:0{{ name }}crwdne8954:0", "UPDATE_VENDOR": "crwdns8956:0{{ name }}crwdne8956:0", "REMOVE_VENDOR": "crwdns8958:0{{ name }}crwdne8958:0", "INVALID_VENDOR_NAME": "crwdns2819:0crwdne2819:0"}, "EDIT_ORGANIZATIONS_EXPENSE_CATEGORIES": {"ADD_EXPENSE_CATEGORY": "crwdns8960:0{{ name }}crwdne8960:0", "UPDATE_EXPENSE_CATEGORY": "crwdns8962:0{{ name }}crwdne8962:0", "REMOVE_EXPENSE_CATEGORY": "crwdns8964:0{{ name }}crwdne8964:0", "INVALID_EXPENSE_CATEGORY_NAME": "crwdns3316:0crwdne3316:0"}, "EDIT_ORGANIZATIONS_POSITIONS": {"ADD_POSITION": "crwdns8966:0{{ name }}crwdne8966:0", "UPDATED_POSITION": "crwdns8968:0{{ name }}crwdne8968:0", "REMOVE_POSITION": "crwdns8970:0{{ name }}crwdne8970:0", "INVALID_POSITION_NAME": "crwdns2825:0crwdne2825:0"}, "EDIT_ORGANIZATIONS_DEPARTMENTS": {"ADD_DEPARTMENT": "crwdns8972:0{{ name }}crwdne8972:0", "REMOVE_DEPARTMENT": "crwdns8974:0{{ name }}crwdne8974:0", "INVALID_DEPARTMENT_NAME": "crwdns2831:0crwdne2831:0"}, "EDIT_ORGANIZATIONS_CLIENTS": {"ADD_CLIENT": "crwdns9896:0{{ name }}crwdne9896:0", "REMOVE_CLIENT": "crwdns9898:0{{ name }}crwdne9898:0", "INVALID_CLIENT_DATA": "crwdns2837:0crwdne2837:0", "INVITE_CLIENT": "crwdns9900:0{{ name }}crwdne9900:0", "INVITE_CLIENT_ERROR": "crwdns2989:0crwdne2989:0", "EMAIL_EXISTS": "crwdns2991:0crwdne2991:0"}, "EDIT_ORGANIZATIONS_CONTACTS": {"ADD_CONTACT": "crwdns8976:0{{ name }}crwdne8976:0", "UPDATE_CONTACT": "crwdns10502:0{{ name }}crwdne10502:0", "REMOVE_CONTACT": "crwdns8980:0{{ name }}crwdne8980:0", "INVALID_CONTACT_DATA": "crwdns5124:0crwdne5124:0", "INVITE_CONTACT": "crwdns8982:0{{ name }}crwdne8982:0", "INVITE_CONTACT_ERROR": "crwdns5128:0crwdne5128:0", "EMAIL_EXISTS": "crwdns5130:0crwdne5130:0"}, "EDIT_ORGANIZATIONS_EMPLOYMENT_TYPES": {"ADD_EMPLOYMENT_TYPE": "crwdns8984:0{{ name }}crwdne8984:0", "UPDATE_EMPLOYMENT_TYPE": "crwdns8986:0{{ name }}crwdne8986:0", "INVALID_EMPLOYMENT_TYPE": "crwdns3067:0crwdne3067:0", "DELETE_EMPLOYMENT_TYPE": "crwdns8988:0{{ name }}crwdne8988:0"}, "EDIT_ORGANIZATIONS_RECURRING_EXPENSES": {"ADD_RECURRING_EXPENSE": "crwdns8990:0{{ name }}crwdne8990:0", "UPDATE_RECURRING_EXPENSE": "crwdns8992:0{{ name }}crwdne8992:0", "DELETE_RECURRING_EXPENSE": "crwdns8994:0crwdne8994:0"}, "EDIT_ORGANIZATIONS_AWARDS": {"ADD_AWARD": "crwdns9902:0{{ name }}crwdne9902:0", "INVALID_AWARD_NAME_YEAR": "crwdns4660:0crwdne4660:0", "REMOVE_AWARD": "crwdns9904:0{{ name }}crwdne9904:0"}, "EDIT_ORGANIZATIONS_LANGUAGES": {"ADD_LANGUAGE": "crwdns9906:0{{ name }}crwdne9906:0", "INVALID_LANGUAGE_NAME_LEVEL": "crwdns4666:0crwdne4666:0", "REMOVE_LANGUAGE": "crwdns9908:0{{ name }}crwdne9908:0"}, "EDIT_ORGANIZATION_DOCS": {"CREATED": "crwdns8996:0{{ name }}crwdne8996:0", "ERR_CREATE": "crwdns7434:0crwdne7434:0", "ERR_LOAD": "crwdns7436:0crwdne7436:0", "UPDATED": "crwdns8998:0{{ name }}crwdne8998:0", "ERR_UPDATED": "crwdns7440:0crwdne7440:0", "SELECTED_DOC": "crwdns7442:0crwdne7442:0", "DELETED": "crwdns9000:0{{ name }}crwdne9000:0", "ERR_DELETED": "crwdns7446:0crwdne7446:0"}}, "DANGER_ZONE": {"WRONG_INPUT_DATA": "crwdns10320:0crwdne10320:0", "ACCOUNT_DELETED": "crwdns2841:0crwdne2841:0", "ALL_DATA_DELETED": "crwdns10322:0crwdne10322:0", "RECORD_TYPE": "crwdns10324:0{{ type }}crwdne10324:0", "TITLES": {"ACCOUNT": "crwdns10326:0crwdne10326:0", "ALL_DATA": "crwdns10328:0crwdne10328:0"}}, "EVENT_TYPES": {"ADD_EVENT_TYPE": "crwdns9002:0{{ name }}crwdne9002:0", "EDIT_EVENT_TYPE": "crwdns9004:0{{ name }}crwdne9004:0", "DELETE_EVENT_TYPE": "crwdns9006:0{{ name }}crwdne9006:0", "ERROR": "crwdns4176:0{{ error }}crwdne4176:0"}, "AVAILABILITY_SLOTS": {"SAVE": "crwdns8432:0crwdne8432:0", "ERROR": "crwdns4260:0{{ error }}crwdne4260:0"}}, "TIMER_TRACKER": {"IS_BILLABLE": "crwdns3224:0crwdne3224:0", "STOP_TIMER": "crwdns3226:0crwdne3226:0", "START_TIMER": "crwdns3228:0crwdne3228:0", "TIMER": "crwdns3230:0crwdne3230:0", "MANUAL": "crwdns3232:0crwdne3232:0", "MANUAL_NOT_ALLOW": "crwdns3552:0crwdne3552:0", "SELECT_PROJECT": "crwdns3234:0crwdne3234:0", "SELECT_TASK": "crwdns3236:0crwdne3236:0", "SELECT_CLIENT": "crwdns7554:0crwdne7554:0", "DATE": "crwdns3238:0crwdne3238:0", "START_TIME": "crwdns3240:0crwdne3240:0", "END_TIME": "crwdns3242:0crwdne3242:0", "DESCRIPTION": "crwdns3244:0crwdne3244:0", "ADD_TIME_SUCCESS": "crwdns9008:0crwdne9008:0", "VALIDATION": {"CLIENT_REQUIRED": "crwdns7556:0crwdne7556:0", "PROJECT_REQUIRED": "crwdns7558:0crwdne7558:0", "TASK_REQUIRED": "crwdns7560:0crwdne7560:0", "DESCRIPTION_REQUIRED": "crwdns7562:0crwdne7562:0"}, "VIEW_TIMESHEET": "crwdns7564:0crwdne7564:0", "ADD_TIME": "crwdns7566:0crwdne7566:0", "TODAY": "crwdns7568:0crwdne7568:0"}, "TIMESHEET": {"TODAY": "crwdns4690:0crwdne4690:0", "DAILY": "crwdns3452:0crwdne3452:0", "WEEKLY": "crwdns3858:0crwdne3858:0", "MONTHLY": "crwdns11072:0crwdne11072:0", "CALENDAR": "crwdns3860:0crwdne3860:0", "APPROVALS": "crwdns3862:0crwdne3862:0", "APPROVE_SUCCESS": "crwdns4044:0crwdne4044:0", "DENIED_SUCCESS": "crwdns4046:0crwdne4046:0", "SUBMIT_SUCCESS": "crwdns8360:0crwdne8360:0", "UNSUBMIT_SUCCESS": "crwdns4164:0crwdne4164:0", "DELETE_TIMELOG": "crwdns8016:0crwdne8016:0", "SELECT_EMPLOYEE": "crwdns4692:0crwdne4692:0", "ALL_EMPLOYEE": "crwdns4694:0crwdne4694:0", "SELECT_SOURCE": "crwdns4696:0crwdne4696:0", "SELECT_ACTIVITY_LEVEL": "crwdns4698:0crwdne4698:0", "SELECT_LOG_TYPE": "crwdns4700:0crwdne4700:0", "ADD_TIME_LOGS": "crwdns4702:0crwdne4702:0", "EDIT_TIME_LOGS": "crwdns4704:0crwdne4704:0", "VIEW_TIME_LOGS": "crwdns4814:0crwdne4814:0", "ADD_TIME": "crwdns4706:0crwdne4706:0", "UPDATE_TIME": "crwdns4708:0crwdne4708:0", "TILL_NOW": "crwdns4858:0crwdne4858:0", "VIEW": "crwdns4860:0crwdne4860:0", "EDIT": "crwdns4816:0crwdne4816:0", "CLOSE": "crwdns4710:0crwdne4710:0", "DELETE": "crwdns4818:0crwdne4818:0", "IMMUTABLE_TIME": "crwdns4862:0crwdne4862:0", "BULK_ACTION": "crwdns7570:0crwdne7570:0", "LOG_TYPE": "crwdns4868:0crwdne4868:0", "SOURCE": "crwdns10531:0crwdne10531:0", "TOTAL_TIME": "crwdns4870:0crwdne4870:0", "ACTIVITIES": "crwdns8434:0crwdne8434:0", "APPROVED_AT": "crwdns4874:0crwdne4874:0", "SUBMITTED_AT": "crwdns4876:0crwdne4876:0", "STATUS": "crwdns4878:0crwdne4878:0", "SUBMIT_TIMESHEET": "crwdns4880:0crwdne4880:0", "UNSUBMIT_TIMESHEET": "crwdns4882:0crwdne4882:0", "APPROVE": "crwdns4884:0crwdne4884:0", "DENY": "crwdns4886:0crwdne4886:0", "TIME_SPAN": "crwdns4888:0crwdne4888:0", "ACTION": "crwdns4890:0crwdne4890:0", "EMPLOYEE": "crwdns4892:0crwdne4892:0", "DURATION": "crwdns4894:0crwdne4894:0", "ORGANIZATION_CONTACT": "crwdns7572:0crwdne7572:0", "NO_ORGANIZATION_CONTACT": "crwdns7574:0crwdne7574:0", "PROJECT": "crwdns4896:0crwdne4896:0", "NO_PROJECT": "crwdns4898:0crwdne4898:0", "NO_TIMELOG": "crwdns4900:0crwdne4900:0", "TODO": "crwdns4902:0crwdne4902:0", "NO_TODO": "crwdns4904:0crwdne4904:0", "OVERLAP_MESSAGE": "crwdns4906:0crwdne4906:0", "REASON": "crwdns7576:0crwdne7576:0", "NOTES": "crwdns8044:0crwdne8044:0", "TIME_OVERLAPS": "crwdns8046:0crwdne8046:0", "TIME_TRACKING": "crwdns8048:0crwdne8048:0", "MEMBERS_WORKED": "crwdns8050:0crwdne8050:0", "PROJECTS_WORKED": "crwdns8052:0crwdne8052:0", "ACTIVITY_OVER_PERIOD": "crwdns11074:0crwdne11074:0", "ACTIVITY_FOR_DAY": "crwdns11076:0crwdne11076:0", "ACTIVITY_FOR_WEEK": "crwdns11078:0crwdne11078:0", "WORKED_THIS_WEEK": "crwdns8056:0crwdne8056:0", "WORKED_OVER_PERIOD": "crwdns10919:0crwdne10919:0", "WORKED_FOR_DAY": "crwdns11080:0crwdne11080:0", "WORKED_FOR_WEEK": "crwdns11082:0crwdne11082:0", "TODAY_ACTIVITY": "crwdns8058:0crwdne8058:0", "WORKED_TODAY": "crwdns8060:0crwdne8060:0", "RECENT_ACTIVITIES": "crwdns8062:0crwdne8062:0", "NO_SCREENSHOT_DAY": "crwdns10887:0crwdne10887:0", "NO_SCREENSHOT_WEEK": "crwdns10889:0crwdne10889:0", "NO_SCREENSHOT_PERIOD": "crwdns10891:0crwdne10891:0", "TASKS": "crwdns8066:0crwdne8066:0", "NO_TASK_ACTIVITY_DAY": "crwdns10893:0crwdne10893:0", "NO_TASK_ACTIVITY_WEEK": "crwdns10895:0crwdne10895:0", "NO_TASK_ACTIVITY_PERIOD": "crwdns10897:0crwdne10897:0", "MANUAL_TIME": "crwdns8070:0crwdne8070:0", "NO_MANUAL_TIME_DAY": "crwdns10899:0crwdne10899:0", "NO_MANUAL_TIME_WEEK": "crwdns10901:0crwdne10901:0", "NO_MANUAL_TIME_PERIOD": "crwdns10903:0crwdne10903:0", "PROJECTS": "crwdns8074:0crwdne8074:0", "NO_PROJECT_ACTIVITY_DAY": "crwdns10905:0crwdne10905:0", "NO_PROJECT_ACTIVITY_WEEK": "crwdns10907:0crwdne10907:0", "NO_PROJECT_ACTIVITY_PERIOD": "crwdns10909:0crwdne10909:0", "APPS_URLS": "crwdns8078:0crwdne8078:0", "NO_APP_URL_ACTIVITY_DAY": "crwdns10911:0crwdne10911:0", "NO_APP_URL_ACTIVITY_WEEK": "crwdns10913:0crwdne10913:0", "NO_APP_URL_ACTIVITY_PERIOD": "crwdns10915:0crwdne10915:0", "DATE": "crwdns8082:0crwdne8082:0", "MEMBERS": "crwdns8084:0crwdne8084:0", "MEMBER": "crwdns8086:0crwdne8086:0", "MEMBER_INFO": "crwdns8088:0crwdne8088:0", "THIS_WEEK": "crwdns8090:0crwdne8090:0", "OVER_PERIOD": "crwdns10917:0crwdne10917:0", "NO_MEMBER_ACTIVITY_DAY": "crwdns10921:0crwdne10921:0", "NO_MEMBER_ACTIVITY_WEEK": "crwdns10923:0crwdne10923:0", "NO_MEMBER_ACTIVITY_PERIOD": "crwdns10925:0crwdne10925:0", "TITLE": "crwdns9984:0crwdne9984:0", "URL": "crwdns9986:0crwdne9986:0", "TIME_SPENT": "crwdns9988:0crwdne9988:0", "ACTIVITY_LEVEL": "crwdns9990:0crwdne9990:0", "VALIDATION": {"DESCRIPTION": "crwdns4712:0crwdne4712:0", "TASK": "crwdns8364:0crwdne8364:0", "PROJECT": "crwdns8366:0crwdne8366:0", "EMPLOYEE": "crwdns8368:0crwdne8368:0", "REASON": "crwdns8370:0crwdne8370:0"}, "SCREENSHOTS": {"SCREENSHOTS": "crwdns9992:0crwdne9992:0", "OF": "crwdns9994:0crwdne9994:0", "TIME_LOG": "crwdns9996:0crwdne9996:0"}, "RUNNING_TIMER_WARNING": "crwdns10875:0crwdne10875:0", "DELETE_CONFIRM": "crwdns10952:0crwdne10952:0", "VIEW_LOG": "crwdns11160:0crwdne11160:0", "NO_DATA": {"DAILY_TIMESHEET": "crwdns11435:0crwdne11435:0", "WEEKLY_TIMESHEET": "crwdns11437:0crwdne11437:0", "APPROVAL_TIMESHEET": "crwdns11439:0crwdne11439:0"}, "VIEW_WINDOWS": "crwdns11453:0crwdne11453:0", "VIEW_WIDGETS": "crwdns11455:0crwdne11455:0"}, "ACTIVITY": {"SCREENSHOTS": "crwdns4944:0crwdne4944:0", "PERCENT_USED": "crwdns8372:0crwdne8372:0", "TIME_SPENT": "crwdns11126:0crwdne11126:0", "APPS": "crwdns8376:0crwdne8376:0", "VISITED_SITES": "crwdns8378:0crwdne8378:0", "NO_ACTIVITIES": "crwdns11441:0crwdne11441:0", "NO_ACTIVITY": "crwdns7146:0crwdne7146:0", "TIME_AND_ACTIVITIES": "crwdns8382:0crwdne8382:0", "VIEW_SCREEN": "crwdns7148:0crwdne7148:0", "VIEW_INFO": "crwdns7150:0crwdne7150:0", "MINUTES": "crwdns8094:0crwdne8094:0", "NO_EMPLOYEES_SELECTED": "crwdns10256:0crwdne10256:0", "DELETE_CONFIRM": "crwdns8096:0crwdne8096:0", "VISITED_DATES": "crwdns11130:0crwdne11130:0", "NO_SCREENSHOT": "crwdns11443:0crwdne11443:0", "NO_SCREENSHOTS": "crwdns11445:0crwdne11445:0", "NO_RECORD_FOUND": "crwdns11447:0crwdne11447:0"}, "ONBOARDING": {"FIRST_ORGANIZATION": "crwdns3476:0crwdne3476:0", "COMPLETE": "crwdns3478:0crwdne3478:0"}, "VALIDATION": {"FIELD_REQUIRED": "crwdns4248:0crwdne4248:0", "ENTER_POSITIVE_NUMBER": "crwdns4250:0crwdne4250:0"}, "APPOINTMENTS_PAGE": {"ADD_APPOINTMENT": "crwdns3958:0crwdne3958:0", "EDIT_APPOINTMENT": "crwdns7082:0crwdne7082:0", "SAVE_SUCCESS": "crwdns9010:0crwdne9010:0", "SAVE_FAILED": "crwdns7086:0crwdne7086:0", "CANCEL_APPOINTMENT": "crwdns7088:0crwdne7088:0", "CANCEL_FAIL": "crwdns7090:0crwdne7090:0", "CANCEL_SUCCESS": "crwdns9012:0crwdne9012:0", "DURATION_ERROR": "crwdns4360:0crwdne4360:0", "SELECT_EMPLOYEE": "crwdns7268:0crwdne7268:0", "EMPLOYEE": "crwdns7270:0crwdne7270:0", "BUFFER_TIME": "crwdns9806:0crwdne9806:0", "BUFFER_AT_START": "crwdns9808:0crwdne9808:0", "BUFFER_AT_END": "crwdns9810:0crwdne9810:0", "BREAK_TIME": "crwdns9812:0crwdne9812:0", "ARE_YOU_SURE": "crwdns9814:0crwdne9814:0"}, "EMPLOYEE_SCHEDULES_MODAL": {"EMPLOYEE": "crwdns7482:0crwdne7482:0", "SLOTS_AVAILABLE": "crwdns7484:0crwdne7484:0", "SLOTS_UNAVAILABLE": "crwdns7486:0crwdne7486:0"}, "EVENT_TYPE_PAGE": {"EVENT_TYPE": "crwdns4102:0crwdne4102:0", "MANAGE_EVENT_TYPE": "crwdns4104:0crwdne4104:0", "EVENT_NAME": "crwdns4106:0crwdne4106:0", "EVENT_DURATION": "crwdns4108:0crwdne4108:0", "EVENT_DESCRIPTION": "crwdns4110:0crwdne4110:0", "ACTIVE": "crwdns4112:0crwdne4112:0", "EMPLOYEE": "crwdns9816:0crwdne9816:0", "YES": "crwdns9818:0crwdne9818:0", "NO": "crwdns9820:0crwdne9820:0", "DURATION_UNIT": "crwdns9822:0{{ unit }}crwdne9822:0"}, "SCHEDULE": {"DATE_SPECIFIC_AVAILABILITY": "crwdns4262:0crwdne4262:0", "SELECT_EMPLOYEE": "crwdns8466:0crwdne8466:0", "RECURRING_AVAILABILITY": "crwdns4264:0crwdne4264:0", "DATE_SPECIFIC_AVAILABILITY_TOOLTIP": "crwdns8436:0crwdne8436:0", "RECURRING_AVAILABILITY_TOOLTIP": "crwdns7620:0crwdne7620:0", "MONDAY_FRIDAY": "crwdns9824:0crwdne9824:0", "SUNDAY_THURSDAY": "crwdns9826:0crwdne9826:0"}, "PUBLIC_APPOINTMENTS": {"BOOK_APPOINTMENT": "crwdns7272:0crwdne7272:0", "BOOK_APPOINTMENTS": "crwdns4312:0crwdne4312:0", "SELECT_EVENT_TYPES": "crwdns4314:0crwdne4314:0", "PICK_DATETIME": "crwdns4316:0crwdne4316:0", "DURATION": "crwdns4362:0crwdne4362:0", "EVENT_TYPE": "crwdns4364:0crwdne4364:0", "CONFIRM_APPOINTMENT": "crwdns5088:0crwdne5088:0", "APPOINTMENT_INFO": "crwdns5090:0crwdne5090:0", "DETAILS": "crwdns4562:0crwdne4562:0", "PARTICIPANTS": "crwdns4564:0crwdne4564:0", "HOST": "crwdns4566:0crwdne4566:0", "RESCHEDULE": "crwdns7236:0crwdne7236:0", "CANCEL": "crwdns5092:0crwdne5092:0", "TIMEZONE": "crwdns7394:0crwdne7394:0", "CHANGE": "crwdns7396:0crwdne7396:0", "SELECT_EMPLOYEE_ERROR": "crwdns7274:0crwdne7274:0", "EXPIRED_OR_CANCELLED": "crwdns5094:0crwdne5094:0", "EMAIL_SENT": "crwdns4568:0crwdne4568:0", "NO_ACTIVE_EVENT_TYpES": "crwdns9052:0crwdne9052:0"}, "EMAIL_TEMPLATES_PAGE": {"HEADER": "crwdns10334:0crwdne10334:0", "SAVE": "crwdns4524:0crwdne4524:0", "LABELS": {"LANGUAGE": "crwdns4526:0crwdne4526:0", "TEMPLATE_NAME": "crwdns4528:0crwdne4528:0", "SUBJECT": "crwdns4530:0crwdne4530:0", "EMAIL_BODY": "crwdns4532:0crwdne4532:0", "EMAIL_PREVIEW": "crwdns4534:0crwdne4534:0", "SUBJECT_PREVIEW": "crwdns4536:0crwdne4536:0"}, "TEMPLATE_NAMES": {"password": "crwdns4538:0crwdne4538:0", "welcome-user": "crwdns4540:0crwdne4540:0", "invite-organization-client": "crwdns4542:0crwdne4542:0", "email-estimate": "crwdns4544:0crwdne4544:0", "email-invoice": "crwdns4546:0crwdne4546:0", "invite-employee": "crwdns4548:0crwdne4548:0", "invite-user": "crwdns4550:0crwdne4550:0", "appointment-confirmation": "crwdns7812:0crwdne7812:0", "appointment-cancellation": "crwdns7814:0crwdne7814:0", "equipment": "crwdns7816:0crwdne7816:0", "equipment-request": "crwdns7818:0crwdne7818:0", "timesheet-overview": "crwdns7820:0crwdne7820:0", "timesheet-submit": "crwdns7822:0crwdne7822:0", "timesheet-action": "crwdns7824:0crwdne7824:0", "timesheet-delete": "crwdns7826:0crwdne7826:0", "time-off-report-action": "crwdns7952:0crwdne7952:0", "task-update": "crwdns7954:0crwdne7954:0", "candidate-schedule-interview": "crwdns7828:0crwdne7828:0", "interviewer-interview-schedule": "crwdns7830:0crwdne7830:0", "employee-join": "crwdns10529:0crwdne10529:0", "TEMPLATE_NAMES": "crwdns9828:0crwdne9828:0"}, "HTML_EDITOR": "crwdns11166:0crwdne11166:0"}, "PIPELINES_PAGE": {"HEADER": "crwdns4740:0crwdne4740:0", "HEADER_STAGES": "crwdns4822:0crwdne4822:0", "VIEW_DEALS": "crwdns7196:0crwdne7196:0", "HEADER_FORM_EDIT": "crwdns4742:0crwdne4742:0", "HEADER_FORM_CREATE": "crwdns4744:0crwdne4744:0", "RECORD_TYPE": "crwdns4746:0{{ name }}crwdne4746:0", "ACTIVE": "crwdns9830:0crwdne9830:0", "INACTIVE": "crwdns9832:0crwdne9832:0", "BROWSE": "crwdns11102:0crwdne11102:0", "SEARCH": "crwdns11104:0crwdne11104:0", "SEARCH_PIPELINE": "crwdns11106:0crwdne11106:0", "NAME": "crwdns11108:0crwdne11108:0", "STAGE": "crwdns11110:0crwdne11110:0", "SEARCH_STAGE": "crwdns11112:0crwdne11112:0", "STATUS": "crwdns11114:0crwdne11114:0", "ALL_STATUS": "crwdns11116:0crwdne11116:0", "RESET": "crwdns11118:0crwdne11118:0"}, "PIPELINE_DEALS_PAGE": {"HEADER": "crwdns7198:0crwdne7198:0", "FILTER_BY_STAGE": "crwdns7200:0crwdne7200:0", "RECORD_TYPE": "crwdns7202:0{{ title }}crwdne7202:0", "ALL_STAGES": "crwdns7928:0crwdne7928:0", "LOW": "crwdns9834:0crwdne9834:0", "MEDIUM": "crwdns9836:0crwdne9836:0", "HIGH": "crwdns9838:0crwdne9838:0", "DEAL_DELETED": "crwdns9180:0{{ name }}crwdne9180:0", "DEAL_EDITED": "crwdns9182:0{{ name }}crwdne9182:0", "DEAL_ADDED": "crwdns9184:0{{ name }}crwdne9184:0"}, "PIPELINE_DEAL_EDIT_PAGE": {"HEADER": "crwdns9910:0{{ name }}crwdne9910:0"}, "PIPELINE_DEAL_CREATE_PAGE": {"HEADER": "crwdns9912:0{{ name }}crwdne9912:0", "SELECT_STAGE": "crwdns7208:0crwdne7208:0", "PROBABILITY": "crwdns8438:0crwdne8438:0", "SELECT_CLIENT": "crwdns9840:0crwdne9840:0"}, "DIALOG": {"CONFIRM": "crwdns8018:0crwdne8018:0", "ALERT": "crwdns8020:0crwdne8020:0", "DELETE_CONFIRM": "crwdns8022:0crwdne8022:0"}, "SETTINGS_FILE_STORAGE": {"FILE_PROVIDER": "crwdns8136:0crwdne8136:0", "S3": {"HEADER": "crwdns10956:0crwdne10956:0", "LABELS": {"ACCESS_KEY_ID": "crwdns10958:0crwdne10958:0", "SECRET_ACCESS_KEY": "crwdns10960:0crwdne10960:0", "REGION": "crwdns10962:0crwdne10962:0", "BUCKET": "crwdns10964:0crwdne10964:0"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "crwdns10966:0crwdne10966:0", "SECRET_ACCESS_KEY": "crwdns10968:0crwdne10968:0", "REGION": "crwdns10970:0crwdne10970:0", "BUCKET": "crwdns10972:0crwdne10972:0"}}, "WASABI": {"HEADER": "crwdns10974:0crwdne10974:0", "LABELS": {"ACCESS_KEY_ID": "crwdns10976:0crwdne10976:0", "SECRET_ACCESS_KEY": "crwdns10978:0crwdne10978:0", "REGION": "crwdns10980:0crwdne10980:0", "BUCKET": "crwdns10982:0crwdne10982:0", "SERVICE_URL": "crwdns10984:0crwdne10984:0"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "crwdns10986:0crwdne10986:0", "SECRET_ACCESS_KEY": "crwdns10988:0crwdne10988:0", "REGION": "crwdns10990:0crwdne10990:0", "BUCKET": "crwdns10992:0crwdne10992:0", "SERVICE_URL": "crwdns10994:0crwdne10994:0"}}}, "CUSTOM_SMTP_PAGE": {"HEADER": "crwdns9914:0{{ name }}crwdne9914:0", "HOST": "crwdns8392:0crwdne8392:0", "PORT": "crwdns8394:0crwdne8394:0", "SECURE": "crwdns8396:0crwdne8396:0", "AUTH": {"USERNAME": "crwdns8398:0crwdne8398:0", "PASSWORD": "crwdns8400:0crwdne8400:0"}}, "SMS_GATEWAY_PAGE": {"HEADER": "crwdns8440:0crwdne8440:0", "TWILIO": "crwdns10028:0crwdne10028:0"}, "FEATURE_PAGE": {"HEADER": "crwdns9916:0{{ name }}crwdne9916:0"}, "SETTINGS": {"EMAIL_HISTORY": {"EMAIL_ARCHIVED": "crwdns8550:0crwdne8550:0", "ARCHIVE": "crwdns8552:0crwdne8552:0", "HEADER": "crwdns8554:0crwdne8554:0", "FROM": "crwdns8556:0crwdne8556:0", "TO": "crwdns8558:0crwdne8558:0", "DATE": "crwdns8560:0crwdne8560:0", "SUBJECT": "crwdns8562:0crwdne8562:0", "LANGUAGE": "crwdns8564:0crwdne8564:0", "TEMPLATE": "crwdns8566:0crwdne8566:0", "NO_EMAILS_SENT": "crwdns8568:0crwdne8568:0", "SYSTEM": "crwdns9842:0crwdne9842:0", "FILTERS": {"TO": "crwdns9844:0crwdne9844:0", "TEMPLATE_LANGUAGE": "crwdns9846:0crwdne9846:0"}}}, "GAUZY_MAINTENANCE": "crwdns10034:0crwdne10034:0", "LEGAL": {"PRIVACY_POLICY": "crwdns9998:0crwdne9998:0", "TERMS_AND_CONDITIONS": "crwdns10000:0crwdne10000:0"}, "LOADING": "crwdns10002:0crwdne10002:0", "ACCOUNTING_TEMPLATES_PAGE": {"HEADER": "crwdns10168:0crwdne10168:0", "TEMPLATE_NAMES": {"invoice": "crwdns10178:0crwdne10178:0", "estimate": "crwdns10180:0crwdne10180:0", "receipt": "crwdns10236:0crwdne10236:0"}}, "LOGIN_PAGE": {"TITLE": "crwdns10825:0crwdne10825:0", "SUB_TITLE": "crwdns10827:0crwdne10827:0", "REMEMBER_ME_TITLE": "crwdns10829:0crwdne10829:0", "DO_NOT_HAVE_ACCOUNT_TITLE": "crwdns10831:0crwdne10831:0", "FORGOT_PASSWORD_TITLE": "crwdns10833:0crwdne10833:0", "LABELS": {"EMAIL": "crwdns10835:0crwdne10835:0", "PASSWORD": "crwdns10837:0crwdne10837:0"}, "PLACEHOLDERS": {"EMAIL": "crwdns10839:0crwdne10839:0", "PASSWORD": "crwdns10841:0crwdne10841:0"}, "VALIDATION": {"EMAIL_REQUIRED": "crwdns10843:0crwdne10843:0", "EMAIL_REAL_REQUIRED": "crwdns10845:0crwdne10845:0", "PASSWORD_REQUIRED": "crwdns10847:0crwdne10847:0", "PASSWORD_SHOULD_CONTAIN": "crwdns10849:0{{ minLength }}crwdnd10849:0{{ maxLength }}crwdne10849:0"}, "DEMO": {"TITLE": "crwdns10851:0crwdne10851:0", "SUB_TITLE": "crwdns10853:0crwdne10853:0"}}, "FORGOT_PASSWORD_PAGE": {"TITLE": "crwdns11169:0crwdne11169:0", "SUB_TITLE": "crwdns11171:0crwdne11171:0", "ALERT_TITLE": "crwdns11173:0crwdne11173:0", "ALERT_SUCCESS_TITLE": "crwdns11175:0crwdne11175:0", "REQUEST_PASSWORD_TEXT": "crwdns11177:0crwdne11177:0", "BACK_TO_LOGIN": "crwdns11179:0crwdne11179:0", "FAQ_TITLE": "crwdns11181:0crwdne11181:0", "FAQ_LEARN_MORE": "crwdns11183:0crwdne11183:0", "LABELS": {"EMAIL": "crwdns11185:0crwdne11185:0"}, "PLACEHOLDERS": {"EMAIL": "crwdns11187:0crwdne11187:0"}, "VALIDATION": {"EMAIL_REQUIRED": "crwdns11189:0crwdne11189:0", "EMAIL_REAL_REQUIRED": "crwdns11191:0crwdne11191:0"}}, "PAGINATION": {"ITEMS": "crwdns11008:0crwdne11008:0"}, "USER_MENU": {"STATUS": "crwdns11024:0crwdne11024:0", "AVAILABLE": "crwdns11026:0crwdne11026:0", "UNAVAILABLE": "crwdns11028:0crwdne11028:0", "PAUSE_NOTIFICATIONS": "crwdns11030:0crwdne11030:0", "FOR_1_HOUR": "crwdns11032:0crwdne11032:0", "FOR_2_HOURS": "crwdns11034:0crwdne11034:0", "UNTIL_TOMORROW": "crwdns11036:0crwdne11036:0", "CUSTOM": "crwdns11038:0crwdne11038:0", "SET_AS_NOTIFICATION_SCHEDULE": "crwdns11040:0crwdne11040:0", "SET_YOURSELF_AS_AWAY": "crwdns11042:0crwdne11042:0", "HOTKEYS": "crwdns11044:0crwdne11044:0", "HELP": "crwdns11046:0crwdne11046:0", "SIGN_OUT": "crwdns11048:0crwdne11048:0", "PROFILE": "crwdns11050:0crwdne11050:0"}, "WORKSPACES": {"MENUS": {"SING_ANOTHER_WORKSPACE": "crwdns11058:0crwdne11058:0", "CREATE_NEW_WORKSPACE": "crwdns11060:0crwdne11060:0", "FIND_WORKSPACE": "crwdns11062:0crwdne11062:0"}}, "NO_IMAGE": {"ADD_DROP": "crwdns11241:0crwdne11241:0", "AVAILABLE": "crwdns11243:0crwdne11243:0"}}