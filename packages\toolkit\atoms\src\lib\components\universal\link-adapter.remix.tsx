import React from 'react';
import { Link as RemixLink } from '@remix-run/react';

export interface LinkAdapterProps {
    href: string;
    children: React.ReactNode;
    className?: string;
    target?: string;
    rel?: string;
    onClick?: () => void;
    prefetch?: "intent" | "render" | "none";
}

export const LinkAdapter = ({ href, children, prefetch, ...rest }: LinkAdapterProps) => (
    <RemixLink to={href} prefetch={prefetch} {...rest}>
        {children}
    </RemixLink>
);
