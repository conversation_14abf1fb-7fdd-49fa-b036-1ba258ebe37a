const COOKIE_NAME = 'cloc-cookie-function';
const DEFAULT_PATH = '/';

interface CookieOptions {
	expires?: Date;
	maxAge?: number;
	domain?: string;
	path?: string; // default: '/'
	secure?: boolean;
	httpOnly?: boolean;
	sameSite?: boolean | 'lax' | 'strict' | 'none';
	priority?: 'low' | 'medium' | 'high';
	encode?: (value: string) => string;
	partitioned?: boolean;
}

const getClocCookie = (): string | null => {
	const cookies = document.cookie.split('; ');
	const cookie = cookies.find((c) => c.startsWith(`${COOKIE_NAME}=`));
	return cookie ? cookie.split('=')[1] : null;
};

const deleteClocCookie = () => {
	document.cookie = `${COOKIE_NAME}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
};

const setClocCookie = (data: string, options: CookieOptions) => {
	const { expires, path = DEFAULT_PATH, ...restOptions } = options;

	document.cookie = `cloc-session=${data}; path=${path}; expires=${expires?.toUTCString()}; ${Object.entries(
		restOptions
	)
		.map(([key, value]) => `${key}=${value}`)
		.join('; ')}`;
};

export { getClocCookie, deleteClocCookie, setClocCookie };
