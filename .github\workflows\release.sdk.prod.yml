name: Release Cloc SDK (Private) to npm

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  release:
    name: Version and SDK Publish  with Changesets
    runs-on: ubuntu-latest

    permissions:
      contents: write # for changelog/version PRs
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          registry-url: 'https://registry.npmjs.org'
          always-auth: true
          token: ${{ secrets.NPM_TOKEN }}

      - name: Install dependencies (Yarn)
        run: yarn install --frozen-lockfile

      - name: Build SDK package
        run: yarn build:atoms

      - name: Create version bump commits if there are changesets
        run: |
          yarn version-packages
          git config user.name "github-actions[bot]"
          git config user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git add .
          git commit -m "ci: sdk version bump via changeset" || echo "No changes to commit"
          git push

      - name: Publish changed packages using Changesets
        uses: changesets/action@v1
        with:
          publish: yarn publish-packages
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
