import { cn } from '@cloc/ui';
import type { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

function ClocTimerFooter({ className }: { className?: string }): ReactElement {
	const { t } = useTranslation();
	return (
		<div
			className={cn(
				'flex justify-center items-center dark:text-white text-black text-[0.6rem] font-normal',
				className
			)}
		>
			<h1>
				{t('FOOTER.powered_by')}{' '}
				<a className=" font-bold underline" href="https://cloc.ai/" target="__blank">
					{t('ORGANIZATION')} {t('TITLE')}
				</a>
			</h1>
		</div>
	);
}

export { ClocTimerFooter };
