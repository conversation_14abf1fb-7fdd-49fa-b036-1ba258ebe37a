import { transformData } from '@cloc/ui';
import { useEffect, useState } from 'react';
import { ChartType } from '../../cloc-report/basic-report/basic-report';
import { Bar<PERSON>hart } from '../cloc-ui-charts/bar-chart/bar-chart';
import { LineChart } from '../cloc-ui-charts/line-chart/line-chart';
import { AreaChart } from '../cloc-ui-charts/area-chart/area-chart';
import { TooltipChart } from '../cloc-ui-charts/tooltip-chart/tooltip-chart';
import { RadarChart } from '../cloc-ui-charts/radar-chart/radar-chart';
import { Radial<PERSON>hart } from '../cloc-ui-charts/radial-chart/radial-chart';
import { useClocContext } from '@lib/context/cloc-context';
import { SpinOverlayLoader } from '@components/loaders/spin-overlay-loader';
import { useTranslation } from 'react-i18next';

const ClocChart: React.FC<{ type: ChartType; className?: string }> = ({ type = 'bar', className }) => {
	const {
		config,
		report,
		appliedTheme,
		loadings: { reportLoading }
	} = useClocContext();
	const [data, setData] = useState(() => transformData(report));

	const { t } = useTranslation(undefined, { keyPrefix: 'NO_DATA' });

	useEffect(() => {
		setData(transformData(report));
	}, [report]);

	return (
		<div className={'min-w-[400px] relative ' + className}>
			{reportLoading && <SpinOverlayLoader />}

			{report && report[0] ? (
				<>
					{type == 'bar' && (
						<BarChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
					)}
					{type == 'bar-vertical' && (
						<BarChart
							color={appliedTheme.colors?.borderColor as string}
							config={config}
							data={data}
							layout="vertical"
						/>
					)}
					{type == 'line' && (
						<LineChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
					)}
					{type == 'area' && (
						<AreaChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
					)}
					{type == 'tooltip' && (
						<TooltipChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
					)}
					{type == 'radar' && (
						<RadarChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
					)}
					{type == 'radial' && (
						<RadialChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
					)}
				</>
			) : (
				<div className="w-full text-slate-400 dark:text-slate-600 flex top-1/2 left-1/2 justify-center items-center absolute -translate-x-2/4 -translate-y-2/4 ">
					{t('no_data_available')}
				</div>
			)}
		</div>
	);
};

ClocChart.displayName = 'ClocChart';

export { ClocChart };
