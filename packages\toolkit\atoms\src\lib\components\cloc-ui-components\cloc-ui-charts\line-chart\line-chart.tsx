'use client';

import { Cartesian<PERSON>rid, <PERSON>, LineChart as Rechart<PERSON>ine<PERSON><PERSON>, XAxis } from 'recharts';

import { ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '../chart';

import { IChartProps } from '@cloc/types';
import { ChartContainer } from '../chart-container';
import { useClocContext } from '@lib/context/cloc-context';
import { generateRandomColors } from '@cloc/ui';
const LineChart: React.FC<IChartProps> = ({ color, data, config, title, description, footer }) => {
	const { config: contextConfig } = useClocContext();
	const finalConfig = config || contextConfig;

	const hasData = data.length > 0;
	const colors = color && hasData ? generateRandomColors(color, Object.keys(data[0]).length) : '';

	return (
		<ChartContainer title={title} description={description} footer={footer} config={finalConfig}>
			<RechartLineChart
				accessibilityLayer
				data={data}
				margin={{
					left: 12,
					right: 12
				}}
			>
				<CartesianGrid vertical={false} />
				<ChartLegend
					className="w-full flex flex-wrap justify-center items-center"
					content={<ChartLegendContent />}
				/>
				<XAxis
					dataKey={hasData ? Object.keys(data[0])[0] : ''}
					tickLine={false}
					axisLine={false}
					tickMargin={8}
					tickFormatter={(value) => value.split('-')[2] + '/' + value.split('-')[1]}
				/>
				<ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
				{/* <Line
							dataKey="desktop"
							type="natural"
							stroke="var(--color-desktop)"
							strokeWidth={2}
							dot={false}
						/> */}
				{hasData &&
					Object.keys(data[0]).map((item, index) => {
						return (
							index !== 0 && (
								<Line
									dataKey={item}
									stroke={colors[index - 1]}
									type="bump"
									// type="linear"
									strokeWidth={1}
									dot={true}
									key={index}
								>
									{/* <LabelList
										position="top"
										offset={12}
										className="fill-foreground"
										fontSize={12}
										content={<ChartCustomLabel width={3} x={3} y={5} />}
									/> */}
								</Line>
							)
						);
					})}
			</RechartLineChart>
		</ChartContainer>
	);
};
LineChart.displayName = 'LineChart';
export { LineChart };
