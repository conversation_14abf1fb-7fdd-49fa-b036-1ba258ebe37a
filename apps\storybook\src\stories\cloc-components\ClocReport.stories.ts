import type { <PERSON>a, StoryObj } from '@storybook/react';
import { BasicClocReport, ModernCloc } from '@cloc/atoms';

const meta = {
	title: 'Clock/Cloc Report',
	component: BasicClocReport,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof BasicClocReport>;

export default meta;
type Story = StoryObj<typeof meta>;

export const AreaChartReport: Story = {
	args: {
		type: 'area'
	}
};

export const TooltipChartReport: Story = {
	args: {
		type: 'tooltip'
	}
};

export const LineChartReport: Story = {
	args: {
		type: 'line'
	}
};

export const BarVerticalChartReport: Story = {
	args: {
		type: 'bar-vertical'
	}
};

export const BarChartReport: Story = {
	args: {
		type: 'bar'
	}
};
