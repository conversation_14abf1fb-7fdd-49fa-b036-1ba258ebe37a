import { VariantProps } from 'class-variance-authority';
import { ClocBaseList, clocBaseListVariants } from './base';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

interface IClocProjectsListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
}

const ClocProjectsList = ({ variant, size, className }: IClocProjectsListProps) => {
	const {
		projectsStats,
		loadings: { projectsStatsLoading }
	} = useClocContext();
	const { t } = useTranslation();

	return (
		<ClocBaseList
			stats={{ data: projectsStats, loading: projectsStatsLoading }}
			title={t('COMMON.project')}
			className={className}
			variant={variant}
			size={size}
		/>
	);
};

export { ClocProjectsList };
