import { Select } from '@cloc/ui';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

export const ClocActiveTeamSelector = ({
	size,
	label,
	className
}: {
	size?: 'default' | 'sm' | 'lg' | null;
	label?: string;
	className?: string;
}) => {
	const {
		organizationTeams: teams,
		setSelectedTeam,
		selectedTeam,
		loadings: { organisationsLoading }
	} = useClocContext();

	const { t } = useTranslation();

	return (
		<div className=" flex flex-col gap-2 ">
			{label && (
				<label htmlFor="team" className="text-sm ">
					{label}
				</label>
			)}
			<Select
				loading={organisationsLoading}
				className={className}
				name="team"
				size={size}
				placeholder={t('INPUT.team_select.select_team')}
				disabled={organisationsLoading}
				value={selectedTeam}
				defaultValue={selectedTeam}
				// values={[
				// 	{
				// 		label: 'Team 1 (4)',
				// 		value: 'id1'
				// 		// icon: <Avatar  className="size-6 text-xs" src="" fallback="AB" title="Avatar" />
				// 	},
				// 	{
				// 		label: 'Team 2 (4)',
				// 		value: 'id2'
				// 		// icon: <Avatar  className="size-6 text-xs" src="" fallback="AB" title="Avatar" />
				// 	},
				// 	{
				// 		label: 'Team 3 (4)',
				// 		value: 'id3'
				// 		// icon: <Avatar  className="size-6 text-xs" src="" fallback="AB" title="Avatar" />
				// 	}
				// ]}
				values={
					teams
						? [
								{ label: 'All teams', value: 'all' },
								...teams?.items.map((team) => {
									return { label: team.name + ` (${team.members?.length || 0})`, value: team?.id };
								})
							]
						: [{ label: 'All teams', value: 'all' }]
				}
				onValueChange={(e) => {
					setSelectedTeam(e);
				}}
			/>
		</div>
	);
};
