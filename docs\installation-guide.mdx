---
id: installation-guide
title: Installation Guide
sidebar_label: Installation Guide
sidebar_position: 4
---

# Getting Started with SDK

Welcome to the @cloc/atoms SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your React application. Whether you're building a productivity tool, project management system, or team collaboration platform, the @cloc/atoms package provides the React components and UI elements you need.

## System Requirements

Before getting started, ensure your development environment meets these requirements:

### Runtime Requirements

- **Node.js**: 18.0.0 or later
- **React**: 18.0.0 or later
- **React DOM**: 18.0.0 or later
- **TypeScript**: 4.5.0 or later (recommended)

### Package Manager Requirements

Choose one of the following package managers:

- **npm**: 8.0.0 or later
- **yarn**: 1.22.19 or later
- **pnpm**: 7.0.0 or later

### Framework Compatibility

The @cloc/atoms package is compatible with:

- **Next.js**: 13.0.0 or later (App Router and Pages Router)
- **Remix**: 1.0.0 or later
- **Vite**: 4.0.0 or later
- **Create React App**: 5.0.0 or later

### Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

:::info Development Environment
For the best development experience, we recommend using TypeScript with strict mode enabled and a modern code editor with TypeScript support.
:::

## Prerequisites

- An Ever Cloc account with API access
- Basic familiarity with React and TypeScript
- Understanding of React Context and Providers

## Authentication Setup

### 1. Obtain Your API Token

Before diving into the installation, you'll need to set up authentication:

1. Log in to your [Ever Cloc Dashboard](https://cloc.ai/)
2. Navigate to Settings → API Access
3. Click "Generate New Token"
4. Name your token (e.g., "Development Token")
5. Set appropriate permissions (minimum: timer:read, timer:write)
6. Copy the generated token

:::warning Security Notice
Your API token grants access to your Cloc account. Never share it or commit it to version control. Consider using different tokens for development and production environments.
:::

### 2. Configure Environment Variables

Create a `.env.local` file in your project root:

```bash
# Cloc API Configuration
NEXT_PUBLIC_CLOC_API_TOKEN=your_api_token_here
NEXT_PUBLIC_CLOC_API_URL=https://api.cloc.ai/v1
NEXT_PUBLIC_CLOC_ORG_ID=your_organization_id
NEXT_PUBLIC_CLOC_TENANT_ID=your_tenant_id
```

:::tip Environment Variables
For Next.js applications, use `NEXT_PUBLIC_` prefix for client-side variables. For other frameworks, adjust the naming convention accordingly.
:::

## Installation

### Core Package Installation

The @cloc/atoms package requires several peer dependencies to function properly. Choose your preferred package manager:

<details>
<summary><strong>npm Installation</strong></summary>

```bash
# Install the core package and required peer dependencies
npm install @cloc/atoms

# Install required peer dependencies
npm install react@^18.0.0 react-dom@^18.0.0

# Install theme and styling dependencies
npm install @emotion/react@^11.13.5 theme-ui@^0.16.2 next-themes@^0.4.4

# Install additional styling dependencies
npm install tailwindcss@^3.4.1 tailwindcss-animate@^1.0.7 tailwind-merge@^2.3.0

# Install utility dependencies
npm install class-variance-authority@^0.7.0 clsx@^2.1.1
```

</details>

<details>
<summary><strong>yarn Installation</strong></summary>

```bash
# Install the core package and required peer dependencies
yarn add @cloc/atoms

# Install required peer dependencies
yarn add react@^18.0.0 react-dom@^18.0.0

# Install theme and styling dependencies
yarn add @emotion/react@^11.13.5 theme-ui@^0.16.2 next-themes@^0.4.4

# Install additional styling dependencies
yarn add tailwindcss@^3.4.1 tailwindcss-animate@^1.0.7 tailwind-merge@^2.3.0

# Install utility dependencies
yarn add class-variance-authority@^0.7.0 clsx@^2.1.1
```

</details>

<details>
<summary><strong>pnpm Installation</strong></summary>

```bash
# Install the core package and required peer dependencies
pnpm add @cloc/atoms

# Install required peer dependencies
pnpm add react@^18.0.0 react-dom@^18.0.0

# Install theme and styling dependencies
pnpm add @emotion/react@^11.13.5 theme-ui@^0.16.2 next-themes@^0.4.4

# Install additional styling dependencies
pnpm add tailwindcss@^3.4.1 tailwindcss-animate@^1.0.7 tailwind-merge@^2.3.0

# Install utility dependencies
pnpm add class-variance-authority@^0.7.0 clsx@^2.1.1
```

</details>

### CSS Imports

Import the required stylesheets in your application:

```tsx
// In your main CSS file or root component
import '@cloc/atoms/styles.css';

// If using Tailwind CSS, also import
import 'tailwindcss/tailwind.css';
```

:::info Stylesheet Order
Import @cloc/atoms styles before your custom styles to ensure proper CSS cascade and customization capabilities.
:::

## Framework-Specific Setup

### Next.js Setup

<details>
<summary><strong>Next.js App Router (Recommended)</strong></summary>

#### 1. Create Root Layout with Providers

Create or update your root layout (`src/app/layout.tsx`):

```tsx
import { ClocProvider } from '@cloc/atoms';
import { ThemeProvider } from 'next-themes';
import '@cloc/atoms/styles.css';
import './globals.css';

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en" suppressHydrationWarning>
			<body>
				<ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
					<ClocProvider
						config={{
							apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
							token: process.env.NEXT_PUBLIC_CLOC_API_TOKEN,
							organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID,
							tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID
						}}
						theme={{
							colors: {
								primary: '#4CAF50',
								secondary: '#2196F3',
								accent: '#FF9800',
								background: '#FFFFFF'
							}
						}}
					>
						{children}
					</ClocProvider>
				</ThemeProvider>
			</body>
		</html>
	);
}
```

#### 2. Update Next.js Configuration

Create or update `next.config.js`:

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
	experimental: {
		appDir: true
	},
	transpilePackages: ['@cloc/atoms', '@cloc/ui', '@cloc/api', '@cloc/types'],
	webpack: (config) => {
		config.resolve.fallback = {
			...config.resolve.fallback,
			fs: false
		};
		return config;
	}
};

module.exports = nextConfig;
```

</details>

<details>
<summary><strong>Next.js Pages Router</strong></summary>

#### 1. Update \_app.tsx

```tsx
import type { AppProps } from 'next/app';
import { ClocProvider } from '@cloc/atoms';
import { ThemeProvider } from 'next-themes';
import '@cloc/atoms/styles.css';
import '../styles/globals.css';

export default function App({ Component, pageProps }: AppProps) {
	return (
		<ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
			<ClocProvider
				config={{
					apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
					token: process.env.NEXT_PUBLIC_CLOC_API_TOKEN,
					organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID,
					tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID
				}}
			>
				<Component {...pageProps} />
			</ClocProvider>
		</ThemeProvider>
	);
}
```

</details>

### Remix Setup

<details>
<summary><strong>Remix Configuration</strong></summary>

#### 1. Update root.tsx

```tsx
import { Links, LiveReload, Meta, Outlet, Scripts, ScrollRestoration } from '@remix-run/react';
import { ClocProvider } from '@cloc/atoms/universal';
import '@cloc/atoms/styles.css';

export default function App() {
	return (
		<html lang="en">
			<head>
				<meta charSet="utf-8" />
				<meta name="viewport" content="width=device-width,initial-scale=1" />
				<Meta />
				<Links />
			</head>
			<body>
				<ClocProvider
					config={{
						apiUrl: process.env.CLOC_API_URL,
						token: process.env.CLOC_API_TOKEN
					}}
				>
					<Outlet />
				</ClocProvider>
				<ScrollRestoration />
				<Scripts />
				<LiveReload />
			</body>
		</html>
	);
}
```

#### 2. Update remix.config.js

```js
/** @type {import('@remix-run/dev').AppConfig} */
module.exports = {
	ignoredRouteFiles: ['**/.*'],
	serverDependenciesToBundle: ['@cloc/atoms', '@cloc/ui', '@cloc/api', '@cloc/types']
};
```

</details>

### Vite Setup

<details>
<summary><strong>Vite Configuration</strong></summary>

#### 1. Update main.tsx

```tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ClocProvider } from '@cloc/atoms';
import App from './App';
import '@cloc/atoms/styles.css';
import './index.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
	<React.StrictMode>
		<ClocProvider
			config={{
				apiUrl: import.meta.env.VITE_CLOC_API_URL,
				token: import.meta.env.VITE_CLOC_API_TOKEN
			}}
		>
			<App />
		</ClocProvider>
	</React.StrictMode>
);
```

#### 2. Update vite.config.ts

```ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
	plugins: [react()],
	optimizeDeps: {
		include: ['@cloc/atoms', '@cloc/ui', '@cloc/api', '@cloc/types']
	},
	define: {
		global: 'globalThis'
	}
});
```

</details>

## Tailwind CSS Configuration

Update your `tailwind.config.js` to include Cloc components and styles:

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
	content: [
		'./src/**/*.{js,ts,jsx,tsx,mdx}',
		'./app/**/*.{js,ts,jsx,tsx,mdx}',
		'./pages/**/*.{js,ts,jsx,tsx,mdx}',
		'./components/**/*.{js,ts,jsx,tsx,mdx}',
		// Include Cloc packages
		'./node_modules/@cloc/atoms/**/*.{js,ts,jsx,tsx}',
		'./node_modules/@cloc/ui/**/*.{js,ts,jsx,tsx}'
	],
	theme: {
		extend: {
			colors: {
				// Cloc theme variables
				primary: 'var(--cloc-primary)',
				secondary: 'var(--cloc-secondary)',
				accent: 'var(--cloc-accent)',
				background: 'var(--cloc-background)',
				foreground: 'var(--cloc-foreground)',
				muted: 'var(--cloc-muted)',
				'muted-foreground': 'var(--cloc-muted-foreground)',
				border: 'var(--cloc-border)',
				input: 'var(--cloc-input)',
				ring: 'var(--cloc-ring)'
			},
			borderRadius: {
				lg: 'var(--cloc-radius)',
				md: 'calc(var(--cloc-radius) - 2px)',
				sm: 'calc(var(--cloc-radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: { height: 0 },
					to: { height: 'var(--radix-accordion-content-height)' }
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: 0 }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require('tailwindcss-animate')],
	darkMode: ['class']
};
```

:::tip Tailwind Configuration
The configuration above includes all necessary content paths and theme variables for optimal integration with @cloc/atoms components.
:::

## Quick Start Example

Create a test component to verify your installation:

### Basic Timer Component

```tsx
// src/app/page.tsx (Next.js App Router)
// or src/pages/index.tsx (Next.js Pages Router)
// or src/App.tsx (Vite/CRA)

import { ModernCloc, ClocThemeToggle } from '@cloc/atoms';

export default function HomePage() {
	return (
		<div className="flex min-h-screen flex-col items-center justify-center p-4 space-y-8">
			<div className="text-center space-y-4">
				<h1 className="text-4xl font-bold text-foreground">Welcome to Cloc SDK</h1>
				<p className="text-muted-foreground max-w-md">Your time tracking components are ready to use!</p>
			</div>

			<div className="flex flex-col items-center space-y-4">
				<ModernCloc expanded={false} showProgress={true} variant="default" size="default" separator=":" />
				<ClocThemeToggle />
			</div>
		</div>
	);
}
```

### Advanced Example with Multiple Components

```tsx
import {
	ModernCloc,
	ClocProvider,
	ClocLoginDialog,
	ClocThemeToggle,
	DailyActivityDisplayer,
	useClocContext
} from '@cloc/atoms';
import { Button } from '@cloc/ui';

function TimerDashboard() {
	const { authenticatedUser } = useClocContext();

	return (
		<div className="container mx-auto p-6 space-y-8">
			<header className="flex justify-between items-center">
				<h1 className="text-3xl font-bold">Time Tracking Dashboard</h1>
				<div className="flex items-center space-x-4">
					<ClocThemeToggle />
					{!authenticatedUser && <ClocLoginDialog />}
				</div>
			</header>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<div className="col-span-1 md:col-span-2 lg:col-span-1">
					<ModernCloc expanded={true} showProgress={true} variant="contained" size="large" />
				</div>

				<div className="col-span-1">
					<DailyActivityDisplayer />
				</div>

				<div className="col-span-1">
					{/* Add more components as needed */}
					<div className="p-4 border rounded-lg">
						<h3 className="font-semibold mb-2">Quick Actions</h3>
						<div className="space-y-2">
							<Button variant="default" className="w-full">
								Start New Timer
							</Button>
							<Button variant="outline" className="w-full">
								View Reports
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default function App() {
	return <TimerDashboard />;
}
```

## Installation Verification

Follow these steps to verify your installation is working correctly:

### 1. Check Package Installation

Verify all packages are installed correctly:

```bash
# Check if @cloc/atoms is installed
npm list @cloc/atoms

# Check peer dependencies
npm list react react-dom @emotion/react theme-ui next-themes

# Check for any missing peer dependencies
npm ls --depth=0
```

### 2. Build Test

Ensure your application builds without errors:

```bash
# For Next.js
npm run build

# For Vite
npm run build

# For Remix
npm run build
```

### 3. Runtime Verification

Start your development server and check for:

```bash
# Start development server
npm run dev
```

**Expected Results:**

- ✅ No console errors related to @cloc/atoms
- ✅ Timer component renders correctly
- ✅ Theme toggle works (if implemented)
- ✅ No missing dependency warnings

### 4. Component Functionality Test

Create a simple test to verify component functionality:

```tsx
// test-cloc-components.tsx
import { ModernCloc, ClocThemeToggle } from '@cloc/atoms';
import { useState } from 'react';

export function ClocComponentTest() {
	const [isExpanded, setIsExpanded] = useState(false);

	return (
		<div className="p-8 space-y-4">
			<h2 className="text-2xl font-bold">Component Test</h2>

			{/* Test 1: Basic Timer */}
			<div className="border p-4 rounded">
				<h3 className="font-semibold mb-2">Basic Timer</h3>
				<ModernCloc variant="default" size="default" />
			</div>

			{/* Test 2: Expandable Timer */}
			<div className="border p-4 rounded">
				<h3 className="font-semibold mb-2">Expandable Timer</h3>
				<button
					onClick={() => setIsExpanded(!isExpanded)}
					className="mb-2 px-4 py-2 bg-blue-500 text-white rounded"
				>
					Toggle Expanded
				</button>
				<ModernCloc expanded={isExpanded} showProgress={true} variant="contained" />
			</div>

			{/* Test 3: Theme Toggle */}
			<div className="border p-4 rounded">
				<h3 className="font-semibold mb-2">Theme Toggle</h3>
				<ClocThemeToggle />
			</div>
		</div>
	);
}
```

:::tip Testing Components
If any component doesn't render or throws errors, check the troubleshooting section below for common solutions.
:::

## Troubleshooting Guide

### Dependency Issues

<details>
<summary><strong>Peer Dependency Warnings</strong></summary>

**Problem:**

```bash
npm WARN @cloc/atoms@0.2.9 requires a peer of react@>=18.0.0 but none is installed
```

**Solutions:**

1. Install missing peer dependencies:

    ```bash
    npm install react@^18.0.0 react-dom@^18.0.0
    ```

2. For version conflicts, check compatibility:

    ```bash
    npm ls react
    ```

3. Force resolution in package.json (use with caution):
    ```json
    {
    	"overrides": {
    		"react": "^18.0.0",
    		"react-dom": "^18.0.0"
    	}
    }
    ```

</details>

<details>
<summary><strong>Module Resolution Errors</strong></summary>

**Problem:**

```bash
Module not found: Can't resolve '@cloc/atoms/styles.css'
```

**Solutions:**

1. Verify package installation:

    ```bash
    npm list @cloc/atoms
    ```

2. Clear node_modules and reinstall:

    ```bash
    rm -rf node_modules package-lock.json
    npm install
    ```

3. Check import path:

    ```tsx
    // Correct
    import '@cloc/atoms/styles.css';

    // Incorrect
    import '@cloc/atoms/dist/styles.css';
    ```

</details>

### Build Errors

<details>
<summary><strong>TypeScript Compilation Errors</strong></summary>

**Problem:**

```bash
Type error: Cannot find module '@cloc/atoms' or its corresponding type declarations
```

**Solutions:**

1. Ensure TypeScript version compatibility:

    ```bash
    npm install typescript@^5.0.0
    ```

2. Add to tsconfig.json:

    ```json
    {
    	"compilerOptions": {
    		"moduleResolution": "node",
    		"esModuleInterop": true,
    		"allowSyntheticDefaultImports": true,
    		"skipLibCheck": true
    	}
    }
    ```

3. Clear TypeScript cache:
    ```bash
    npx tsc --build --clean
    ```

</details>

<details>
<summary><strong>Bundler Configuration Issues</strong></summary>

**Problem:**

```bash
Error: Failed to resolve import "@cloc/atoms" from "src/App.tsx"
```

**Solutions:**

For **Vite**:

```ts
// vite.config.ts
export default defineConfig({
	optimizeDeps: {
		include: ['@cloc/atoms', '@cloc/ui', '@cloc/api', '@cloc/types']
	}
});
```

For **Webpack** (CRA):

```js
// craco.config.js
module.exports = {
	webpack: {
		configure: (webpackConfig) => {
			webpackConfig.resolve.fallback = {
				...webpackConfig.resolve.fallback,
				stream: require.resolve('stream-browserify'),
				buffer: require.resolve('buffer')
			};
			return webpackConfig;
		}
	}
};
```

For **Next.js**:

```js
// next.config.js
module.exports = {
	transpilePackages: ['@cloc/atoms', '@cloc/ui', '@cloc/api', '@cloc/types']
};
```

</details>

### Runtime Errors

<details>
<summary><strong>Provider Configuration Errors</strong></summary>

**Problem:**

```bash
Error: useClocContext must be used within a ClocProvider
```

**Solutions:**

1. Ensure ClocProvider wraps your app:

    ```tsx
    // ✅ Correct
    function App() {
    	return (
    		<ClocProvider
    			config={
    				{
    					/* ... */
    				}
    			}
    		>
    			<YourComponents />
    		</ClocProvider>
    	);
    }

    // ❌ Incorrect
    function App() {
    	return (
    		<div>
    			<ClocProvider
    				config={
    					{
    						/* ... */
    					}
    				}
    			/>
    			<YourComponents />
    		</div>
    	);
    }
    ```

2. Check provider configuration:
    ```tsx
    <ClocProvider
      config={{
        apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL, // Must be defined
        token: process.env.NEXT_PUBLIC_CLOC_API_TOKEN, // Must be defined
      }}
    >
    ```

</details>

<details>
<summary><strong>Authentication Errors</strong></summary>

**Problem:**

```bash
Error: Invalid or missing API token
```

**Solutions:**

1. Verify environment variables:

    ```bash
    # Check if variables are loaded
    console.log(process.env.NEXT_PUBLIC_CLOC_API_TOKEN);
    ```

2. Ensure proper token format:

    ```bash
    # Token should be a valid JWT or API key
    NEXT_PUBLIC_CLOC_API_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
    ```

3. Check token permissions in Cloc dashboard

4. Verify API endpoint:
    ```bash
    NEXT_PUBLIC_CLOC_API_URL=https://api.cloc.ai/v1
    ```

</details>

<details>
<summary><strong>Styling and Theme Issues</strong></summary>

**Problem:**

```bash
Warning: Prop `className` did not match. Server: "..." Client: "..."
```

**Solutions:**

1. Ensure CSS imports order:

    ```tsx
    // 1. Framework styles first
    import 'tailwindcss/tailwind.css';
    // 2. Cloc styles
    import '@cloc/atoms/styles.css';
    // 3. Custom styles last
    import './globals.css';
    ```

2. Add suppressHydrationWarning for theme providers:

    ```tsx
    <html lang="en" suppressHydrationWarning>
    ```

3. Check Tailwind configuration includes Cloc paths:
    ```js
    module.exports = {
    	content: [
    		'./node_modules/@cloc/atoms/**/*.{js,ts,jsx,tsx}'
    		// ... other paths
    	]
    };
    ```

</details>

### Performance Issues

<details>
<summary><strong>Bundle Size Optimization</strong></summary>

**Problem:** Large bundle size affecting performance

**Solutions:**

1. Use tree shaking with named imports:

    ```tsx
    // ✅ Good - tree shakeable
    import { ModernCloc, ClocThemeToggle } from '@cloc/atoms';

    // ❌ Bad - imports everything
    import * as ClocAtoms from '@cloc/atoms';
    ```

2. Lazy load components:

    ```tsx
    import { lazy, Suspense } from 'react';

    const ModernCloc = lazy(() => import('@cloc/atoms').then((module) => ({ default: module.ModernCloc })));

    function App() {
    	return (
    		<Suspense fallback={<div>Loading...</div>}>
    			<ModernCloc />
    		</Suspense>
    	);
    }
    ```

3. Configure bundle analyzer:
    ```bash
    npm install --save-dev @next/bundle-analyzer
    ```

</details>

### Framework-Specific Issues

<details>
<summary><strong>Next.js Specific Issues</strong></summary>

**Problem:** SSR/Hydration mismatches

**Solutions:**

1. Use dynamic imports for client-only components:

    ```tsx
    import dynamic from 'next/dynamic';

    const ModernCloc = dynamic(() => import('@cloc/atoms').then((mod) => mod.ModernCloc), { ssr: false });
    ```

2. Add transpilePackages to next.config.js:
    ```js
    module.exports = {
    	transpilePackages: ['@cloc/atoms', '@cloc/ui']
    };
    ```

</details>

<details>
<summary><strong>Remix Specific Issues</strong></summary>

**Problem:** Server-side rendering errors

**Solutions:**

1. Use universal exports:

    ```tsx
    import { ClocProvider } from '@cloc/atoms/universal';
    ```

2. Configure serverDependenciesToBundle:
    ```js
    // remix.config.js
    module.exports = {
    	serverDependenciesToBundle: ['@cloc/atoms']
    };
    ```

</details>

:::warning Getting Stuck?
If you're still experiencing issues after trying these solutions, please check our [GitHub Issues](https://github.com/ever-co/ever-cloc/issues) or reach out to our support team.
:::

## Advanced Configuration

### Monorepo Setup

If you're working in a monorepo environment:

<details>
<summary><strong>Yarn Workspaces Configuration</strong></summary>

```json
// package.json (root)
{
	"workspaces": ["packages/*", "apps/*"],
	"devDependencies": {
		"@cloc/atoms": "*"
	}
}
```

```json
// apps/web/package.json
{
	"dependencies": {
		"@cloc/atoms": "*",
		"react": "^18.0.0",
		"react-dom": "^18.0.0"
	}
}
```

</details>

### Custom Theming

<details>
<summary><strong>Advanced Theme Customization</strong></summary>

```tsx
// theme.config.ts
export const customTheme = {
	colors: {
		primary: '#6366f1',
		secondary: '#8b5cf6',
		accent: '#f59e0b',
		background: '#ffffff',
		foreground: '#1f2937',
		muted: '#f3f4f6',
		'muted-foreground': '#6b7280',
		border: '#e5e7eb',
		input: '#ffffff',
		ring: '#6366f1'
	},
	borderRadius: {
		lg: '0.5rem',
		md: '0.375rem',
		sm: '0.25rem'
	},
	fonts: {
		sans: ['Inter', 'system-ui', 'sans-serif'],
		mono: ['JetBrains Mono', 'monospace']
	}
};

// App.tsx
<ClocProvider
	config={
		{
			/* ... */
		}
	}
	theme={customTheme}
>
	{children}
</ClocProvider>;
```

</details>

### Internationalization Setup

<details>
<summary><strong>i18n Configuration</strong></summary>

```tsx
// i18n.config.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

i18n.use(initReactI18next).init({
	resources: {
		en: {
			translation: {
				'timer.start': 'Start Timer',
				'timer.stop': 'Stop Timer',
				'timer.pause': 'Pause Timer'
			}
		},
		es: {
			translation: {
				'timer.start': 'Iniciar Temporizador',
				'timer.stop': 'Detener Temporizador',
				'timer.pause': 'Pausar Temporizador'
			}
		}
	},
	lng: 'en',
	fallbackLng: 'en',
	interpolation: {
		escapeValue: false
	}
});

export default i18n;
```

```tsx
// App.tsx
import './i18n.config';
import { ClocLanguageSwitch } from '@cloc/atoms';

function App() {
	return (
		<ClocProvider>
			<ClocLanguageSwitch />
			{/* Your components */}
		</ClocProvider>
	);
}
```

</details>

## Testing Setup

### Unit Testing with Jest

<details>
<summary><strong>Jest Configuration</strong></summary>

```js
// jest.config.js
module.exports = {
	testEnvironment: 'jsdom',
	setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
	moduleNameMapping: {
		'^@cloc/atoms$': '<rootDir>/node_modules/@cloc/atoms/dist/index.es.js',
		'\\.(css|less|scss|sass)$': 'identity-obj-proxy'
	},
	transformIgnorePatterns: ['node_modules/(?!(@cloc/atoms|@cloc/ui|@cloc/api|@cloc/types)/)']
};
```

```tsx
// src/setupTests.ts
import '@testing-library/jest-dom';

// Mock environment variables
process.env.NEXT_PUBLIC_CLOC_API_URL = 'https://api.cloc.ai/v1';
process.env.NEXT_PUBLIC_CLOC_API_TOKEN = 'test-token';
```

</details>

### Component Testing Example

```tsx
// __tests__/ModernCloc.test.tsx
import { render, screen } from '@testing-library/react';
import { ClocProvider, ModernCloc } from '@cloc/atoms';

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
	<ClocProvider
		config={{
			apiUrl: 'https://api.cloc.ai/v1',
			token: 'test-token'
		}}
	>
		{children}
	</ClocProvider>
);

describe('ModernCloc', () => {
	it('renders timer component', () => {
		render(
			<TestWrapper>
				<ModernCloc variant="default" size="default" />
			</TestWrapper>
		);

		expect(screen.getByRole('timer')).toBeInTheDocument();
	});

	it('displays correct time format', () => {
		render(
			<TestWrapper>
				<ModernCloc variant="default" separator=":" />
			</TestWrapper>
		);

		expect(screen.getByText(/\d{2}:\d{2}:\d{2}/)).toBeInTheDocument();
	});
});
```

## Getting Help

Need additional support? We're here to help:

- 📚 [Documentation](https://docs.cloc.ai/)
- 💬 [Discord Community](https://discord.gg/cloc-ai)
- 📧 [Email Support](mailto:<EMAIL>)
- 🐛 [GitHub Issues](https://github.com/ever-co/ever-cloc/issues)
- 📖 [API Reference](https://docs.cloc.ai/api)

## Next Steps

Congratulations! You've successfully installed and configured @cloc/atoms. Here's what to explore next:

:::info Explore the SDK

- **[Component Library](/docs/components)** - Browse all available React components
- **[API Reference](/docs/api-reference)** - Complete API documentation and examples
- **[Tracking Guide](/docs/tracking-package)** - Implement advanced analytics and tracking
- **[Examples](/docs/examples)** - Real-world implementation examples and tutorials

:::

### Quick Links

- 🎯 [Timer Components Guide](/docs/components/timers) - Learn about all timer variants
- 🎨 [Theming Guide](/docs/theming) - Customize the look and feel
- 🔐 [Authentication Guide](/docs/authentication) - Set up user authentication
- 📊 [Analytics Integration](/docs/analytics) - Track user behavior and performance
- 🌍 [Internationalization](/docs/i18n) - Add multi-language support

Start building amazing time-tracking experiences with @cloc/atoms! 🚀

---

**Happy coding!** If you build something awesome with @cloc/atoms, we'd love to hear about it. Share your projects with the community on our Discord server.
