import { Select } from '@cloc/ui';
import useFontSelector from '@hooks/useFontSelector';

const ClocFontToggle = () => {
	const { fontOptions, selectedFont, setSelectedFont } = useFontSelector();
	const optionsForSelect = fontOptions.map(({ name, value }) => ({
		label: name,
		value
	}));
	return (
		<div className="flex gap-2 min-w-[15rem] ">
			<Select
				placeholder="Select font"
				defaultValue={selectedFont}
				values={optionsForSelect}
				onValueChange={(e) => {
					setSelectedFont(e);
				}}
				value={selectedFont}
			/>
		</div>
	);
};
ClocFontToggle.displayName = 'ClocFontToggle';
export { ClocFontToggle };
