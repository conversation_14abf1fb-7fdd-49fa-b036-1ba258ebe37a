import React from 'react';
import { Card, cn, formatTime, getWeekStartAndEnd, Progress, areDatesEqual } from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';
import { SpinOverlayLoader } from '../loaders/spin-overlay-loader';
import { useTranslation } from 'react-i18next';

export interface IReportDisplayer {
	workedTime?: number | undefined;
	icon?: React.ReactNode;
	label: string;
	showProgress?: boolean;
	maxWorkHours: number;
	className?: string;
}

/**
 * A component that displays a report of the time worked
 * @param {{ icon?: React.ReactNode, workedTime?: number, label: string, showProgress?: boolean, maxWorkHours?: number }} props
 * @param {React.ReactNode} [props.icon] An optional icon to display
 * @param {number} [props.workedTime=0] The time worked in seconds
 * @param {string} props.label The label of the report
 * @param {boolean} [props.showProgress=true] Whether to show a progress bar
 * @param {number} [props.maxWorkHours=8] The maximum number of hours worked
 * @returns {React.JSX.Element} The report component
 */
const ClocReportDisplayer: React.FC<IReportDisplayer> = ({
	icon,
	workedTime = 0,
	label,
	showProgress = true,
	maxWorkHours = 8,
	className
}) => {
	const {
		loadings: { statisticsCountsLoading }
	} = useClocContext();
	return (
		<Card
			className={cn(
				'dark:text-white border relative dark:border-gray-600 text-sm rounded-xl p-3 min-w-[150px]  gap-1 inline-flex  flex-col',
				className
			)}
		>
			{statisticsCountsLoading && <SpinOverlayLoader />}
			<div className="flex justify-between items-center dark:text-white/50 text-gray-400">
				<span className="text-xs">{label}</span>
				<span>{icon}</span>
			</div>
			<div className="text-xl font-medium ">{formatTime(workedTime)}</div>
			{showProgress && <Progress className="w-full h-2" value={(workedTime * 100) / (maxWorkHours * 60 * 60)} />}
		</Card>
	);
};

/**
 * A component that displays a report of the time worked today
 * @param {{ showProgress?: boolean }} props
 * @param {boolean} [props.showProgress=true] Whether to show a progress bar
 * @returns {React.JSX.Element} The report component
 */
const DailyWorkedTimeDisplayer = ({
	showProgress = true,
	className
}: {
	showProgress?: boolean;
	className?: string;
}) => {
	const { statisticsCounts } = useClocContext();
	const { t } = useTranslation();
	return (
		<ClocReportDisplayer
			workedTime={statisticsCounts?.todayDuration}
			label={t('REPORT.worked_today')}
			showProgress={showProgress}
			maxWorkHours={8}
			className={className}
		/>
	);
};

/**
 * A component that displays a report of the time worked in current week
 * @param {{ showProgress?: boolean }} props
 * @param {boolean} [props.showProgress=true] Whether to show a progress bar
 * @returns {React.JSX.Element} The report component
 */
const WeeklyWorkedTimeDisplayer = ({
	showProgress = true,
	className
}: {
	showProgress?: boolean;
	className?: string;
}) => {
	const { statisticsCounts, reportDates } = useClocContext();
	const dates = getWeekStartAndEnd();
	const { t } = useTranslation();
	return (
		<ClocReportDisplayer
			workedTime={statisticsCounts?.weekDuration}
			label={
				areDatesEqual(reportDates?.from, dates.start) && areDatesEqual(reportDates?.to, dates.end)
					? t('REPORT.worked_this_week')
					: t('REPORT.worked_over_period')
			}
			showProgress={showProgress}
			maxWorkHours={40}
			className={className}
		/>
	);
};

ClocReportDisplayer.displayName = 'ClocReportDisplayer';
DailyWorkedTimeDisplayer.displayName = 'DailyWorkedTimeDisplayer';
WeeklyWorkedTimeDisplayer.displayName = 'WeeklyReportDisplayer';

export { ClocReportDisplayer, DailyWorkedTimeDisplayer, WeeklyWorkedTimeDisplayer };
