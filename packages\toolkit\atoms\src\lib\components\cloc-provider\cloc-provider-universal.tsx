/** @jsxImportSource theme-ui */
import React, { ReactNode, createContext, useContext } from 'react';
import { ClocProvider } from './cloc-provider';
import { Theme } from 'theme-ui';

interface ClocConfig {
    apiUrl?: string;
    token?: string;
    cookieDomain?: string;
    environment?: 'development' | 'production' | 'test';
    debug?: boolean;
    locale?: string;
    timezone?: string;
    features?: {
        analytics?: boolean;
        notifications?: boolean;
        offline?: boolean;
    };
}

interface NavigationComponentProps {
    href?: string;
    to?: string;
    children: ReactNode;
    className?: string;
    target?: string;
    rel?: string;
    onClick?: () => void;
    prefetch?: boolean;
    replace?: boolean;
}

type NavigationComponent =
    | React.ComponentType<NavigationComponentProps>
    | React.ComponentType<{ href?: string; to?: string; children?: React.ReactNode }>
    | 'a';

interface ClocProviderConfig {
    config: ClocConfig;
    NavigationComponent?: NavigationComponent;
}

const ClocUniversalContext = createContext<ClocProviderConfig | null>(null);

interface ClocProviderUniversalProps {
    children: ReactNode;
    config: ClocConfig;
    NavigationComponent?: NavigationComponent;
    theme?: Theme<{}>;
    lang?: string;
}

/**
 * Framework-agnostic ClocProvider wrapper that provides configuration context.
 *
 * @param children - Child components to wrap
 * @param config - Configuration object with token and optional settings
 * @param NavigationComponent - Optional navigation component for navigation (defaults to 'a')
 * @param theme - Optional theme-ui theme object
 * @param lang - Optional language code
 *
 * @example
 * <ClocProviderUniversal
 *   config={{ token: 'your-auth-token' }}
 *   lang="en"
 * >
 *   <App />
 * </ClocProviderUniversal>
 */
const ClocProviderUniversal: React.FC<ClocProviderUniversalProps> = ({
    children,
    config,
    NavigationComponent = 'a',
    theme,
    lang
}) => {
    const universalConfig = {
        config,
        NavigationComponent
    };

    return (
        <ClocUniversalContext.Provider value={universalConfig}>
            <ClocProvider
                theme={theme}
                token={config.token}
                lang={lang}
            >
                {children}
            </ClocProvider>
        </ClocUniversalContext.Provider>
    );
};

const useClocUniversal = (): ClocProviderConfig => {
    const context = useContext(ClocUniversalContext);
    if (!context) {
        throw new Error(
            'useClocUniversal must be used within a ClocProviderUniversal'
        );
    }
    return context;
};

export { ClocProviderUniversal, useClocUniversal };
export type { ClocConfig, NavigationComponent, ClocProviderUniversalProps };
