/** @jsxImportSource theme-ui */
'use client';

import React from 'react';
import {
	Avatar,
	cn,
	Dialog,
	Input,
	Popover,
	PopoverContent,
	PopoverTrigger,
	<PERSON>bs,
	<PERSON>bsContent,
	<PERSON>bs<PERSON>ist,
	<PERSON>bs<PERSON>rigger,
	ThemedButton
} from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';
import { useAuthForm } from '../../hooks/useAuthForm';
import { CheckIcon, Copy, LogOutIcon } from 'lucide-react';
import { useState } from 'react';
import { Card } from 'theme-ui';
import { useTranslation } from 'react-i18next';
import { SpinOverlayLoader } from '../loaders/spin-overlay-loader';
import { ClocTimerFooter } from '@components/layouts/footers/component-footer';
import { LinkAdapter } from '../universal/link-adapter.native';

type ComponentSource = 'storybook' | 'demo' | 'production';

/**
 * A component to display the user's avatar with a popover menu.
 *
 * This component uses the current authenticated user's information
 * from Cloc Provider to display an avatar image. If the user is loading,
 * a loading spinner is shown. When the avatar is clicked, a popover
 * menu is displayed with further navigation options.
 *
 * @returns {React.JSX.Element | null} The user's avatar and popover menu
 * or null if the user is not authenticated.
 */

const ClocUserAvatar = ({
	children,
	showMenu = true,
	position
}: {
	children?: React.ReactNode;
	showMenu?: boolean;
	position?: 'center' | 'end' | 'start';
}) => {
	const {
		authenticatedUser: user,
		loadings: { userLoading }
	} = useClocContext();

	if (!user) return null;

	if (showMenu)
		return (
			<Popover>
				<PopoverTrigger className="outline-none px-3 relative">
					{userLoading && <SpinOverlayLoader />}
					<Avatar
						title={
							(user.firstName ? user.firstName[0] : '') + ' ' + (user.lastName ? user.lastName[0] : '')
						}
						fallback={(user.firstName ? user.firstName[0] : '') + (user.lastName ? user.lastName[0] : '')}
						src={user.imageUrl}
					/>
				</PopoverTrigger>

				<PopoverContent
					align={position}
					className="z-[var(--z-modal)] w-64 rounded-xl  text-sm/6 transition duration-200 ease-in-out"
				>
					<UserNavMenu>{children}</UserNavMenu>
				</PopoverContent>
			</Popover>
		);

	return (
		<Avatar
			title={(user.firstName ? user.firstName[0] : '') + ' ' + (user.lastName ? user.lastName[0] : '')}
			fallback={(user.firstName ? user.firstName[0] : '') + (user.lastName ? user.lastName[0] : '')}
			src={user.imageUrl}
		/>
	);
};

/**
 * A login form component for Cloc.
 *
 * @prop {string} [className] - The CSS class name for the component.
 * @prop {React.ForwardedRef<HTMLDivElement>} [ref] - A reference to the component.
 *
 * @example
 * <ClocLoginFormUniversal />
 *
 * @returns {JSX.Element} The login form component.
 */
const ClocLoginForm = ({
	ref,
	className,
	signupLink,
	redirectHandler
}: {
	source?: ComponentSource;
	className?: string;
	ref?: React.ForwardedRef<HTMLDivElement>;
	signupLink?: string;
	redirectHandler?: () => void;
}) => {
	const {} = useClocContext();

	const { t } = useTranslation();

	const {} = useAuthForm({
		navigateFunc: () => {
			// Handle navigation if needed
		},
		redirectHandler: () => {
			// Handle redirect if needed
		}
	});

	return (
		<div ref={ref} className={className}>
			<div className=" pb-4 mb-4 flex flex-col gap-4 items-start justify-center border-b text-black dark:text-white   ">
				<h1 className=" text-3xl font-bold tracking-tight">{t('AUTH.sign_in_title')}</h1>
			</div>
			<Tabs className="transition-all flex flex-col gap-3 delay-200" defaultValue="password">
				<TabsList className="bg-primary bg-opacity-20 grid grid-cols-2 h-10 text-white">
					<TabsTrigger value="password">{t('COMMON.password')}</TabsTrigger>
					<TabsTrigger value="token">{t('COMMON.token')}</TabsTrigger>
				</TabsList>
				<TabsContent value="password">
					<PasswordForm redirectHandler={redirectHandler} />
				</TabsContent>
				<TabsContent value="token">
					<TokenForm redirectHandler={redirectHandler} />
				</TabsContent>
			</Tabs>
			{signupLink && (
				<p className="text-slate-500 text-sm dark:text-white pt-3">
					{t('AUTH.dont_have_an_account')}{' '}
					<LinkAdapter className="text-blue-500 cursor-pointer" href={signupLink}>
						{t('AUTH.sign_up_title')}
					</LinkAdapter>
				</p>
			)}
			<ClocTimerFooter className="mt-2" />
		</div>
	);
};

/**
 * A component to display a login dialog.
 *
 * This component displays a dialog with a login form when the user is not authenticated.
 * If the user is authenticated, the component displays the user's avatar.
 *
 * @prop {React.ReactNode} [trigger] - The trigger element to display the dialog.
 * If not provided, a default button with 'SIGN IN' label is displayed.
 *
 * @returns {React.ReactNode} The dialog component.
 */
const ClocLoginDialog = ({
	trigger,
	signupLink,
	redirectHandler
}: {
	trigger?: React.ReactNode;
	signupLink?: string;
	redirectHandler?: () => void;
}) => {
	const { authenticatedUser: user } = useClocContext();
	return (
		<div>
			{!user ? (
				<Dialog trigger={trigger ? trigger : <ThemedButton className="min-w-28">SIGN IN</ThemedButton>}>
					<ClocLoginForm redirectHandler={redirectHandler} signupLink={signupLink} />
				</Dialog>
			) : (
				<ClocUserAvatar />
			)}
		</div>
	);
};

ClocLoginForm.displayName = 'ClocLoginForm';

export function MenuIndicator() {
	return (
		<Card
			className={cn(
				'absolute top-4 -z-10 bg-transparent dark:bg-transparent ',
				'nav-items--shadow rounded-none !py-0 !px-0',
				'w-0 h-0',
				'border-l-[15px] border-r-[15px]',
				'xl:border-l-[35px] border-l-transparent xl:border-r-[35px] border-r-transparent',
				'border-solid border-b-light--theme-light dark:border-b-dark--theme-light border-b-[50px]'
			)}
		/>
	);
}

export const UserNavMenu = ({ children }: { children?: React.ReactNode }) => {
	const { authenticatedUser: user } = useClocContext();
	const { t } = useTranslation();

	const logOut = () => {
		if (typeof window !== 'undefined') {
			const resetStore = {
				app: { user: null },
				persist: { token: null }
			};

			window.localStorage.setItem('_cloc-store', JSON.stringify(resetStore));
			window.location.href = '/';
		}
	};

	return (
		<div className="   text-sm text-gray-800 dark:text-gray-100">
			<div className="flex flex-col justify-center items-center gap-3 pb-3 border-b space-x-3  dark:border-gray-700">
				<Avatar
					title={(user?.firstName ? user.firstName[0] : '') + ' ' + (user?.lastName ? user.lastName[0] : '')}
					fallback={(user?.firstName ? user.firstName[0] : '') + (user?.lastName ? user.lastName[0] : '')}
					src={user?.imageUrl || ''}
				/>

				<div className="text-center">
					<h4 className="font-medium">{user?.firstName + ' ' + user?.lastName}</h4>
					<p className="text-xs text-muted-foreground">{user?.email}</p>
				</div>
			</div>
			<div className="p-2 space-y-2">
				{children}
				{/* {menuItems.map((item, index) => (
					<DropdownMenuItem
						key={index}
						onClick={item.onClick}
						className="flex items-center gap-2 p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
					>
						{item.icon}
						{item.label}
					</DropdownMenuItem>
				))} */}

				<div className="flex items-center gap-2 p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
					<LogOutIcon size={16} />
					<span onClick={logOut}>{t('AUTH.logout')}</span>
				</div>
			</div>
		</div>
	);
};

export const TokenDisplay = ({ token }: { token: string }) => {
	const [copied, setCopied] = useState(false);

	const handleCopy = () => {
		navigator.clipboard.writeText(token);
		setCopied(true);
		setTimeout(() => setCopied(false), 2000);
	};

	return (
		<div className="space-y-2">
			<div className="p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono break-all">{token}</div>
			<button onClick={handleCopy} className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800">
				{copied ? <CheckIcon size={12} /> : <Copy size={12} />}
				{copied ? 'Copied!' : 'Copy Token'}
			</button>
		</div>
	);
};

const PasswordForm = ({}: { redirectHandler?: () => void }) => {
	const { t } = useTranslation();

	const { formData, loading, error, handleInputChange, handleSubmit } = useAuthForm({
		navigateFunc: () => {
			// Handle navigation if needed
		},
		redirectHandler: () => {
			// Handle redirect if needed
		}
	});

	return (
		<form onSubmit={handleSubmit} className="space-y-3">
			<div>
				<label htmlFor="email" className="text-slate-500 dark:text-white text-sm">
					{t('COMMON.email')} :
				</label>
				<Input
					required
					onChange={handleInputChange}
					className="border mt-1"
					placeholder={t('AUTH.email_prompt')}
					value={formData.email}
					size={30}
					type="email"
					name="email"
				/>
			</div>

			<div>
				<label htmlFor="password" className="text-slate-500 dark:text-white text-sm">
					{t('COMMON.password')} :
				</label>
				<Input
					required
					onChange={handleInputChange}
					className="border mt-1"
					placeholder={t('AUTH.password_prompt')}
					value={formData.password}
					size={30}
					type="password"
					name="password"
				/>
			</div>

			{error ? <span className="text-red-500 text-xs">{error}</span> : null}

			<ThemedButton type="submit" disabled={loading} className="w-full flex gap-2 mt-4">
				{loading && <span className="animate-spin">⏳</span>}
				{t('AUTH.sign_in_title').toUpperCase()}
			</ThemedButton>
		</form>
	);
};

const TokenForm = ({}: { redirectHandler?: () => void }) => {
	const { t } = useTranslation();

	const { formData, loading, error, handleInputChange, handleSubmit } = useAuthForm({
		navigateFunc: () => {
			// Handle navigation if needed
		},
		redirectHandler: () => {
			// Handle redirect if needed
		}
	});

	return (
		<form onSubmit={handleSubmit} className="space-y-3">
			<div>
				<label htmlFor="token" className="text-slate-500 dark:text-white text-sm">
					{t('COMMON.token')} :
				</label>
				<Input
					required
					onChange={handleInputChange}
					className="border mt-1"
					placeholder="Enter your access token"
					value={formData.inputToken || ''}
					type="text"
					name="token"
				/>
			</div>

			{error ? <span className="text-red-500 text-xs">{error}</span> : null}

			<ThemedButton type="submit" disabled={loading} className="w-full flex gap-2 mt-4">
				{loading && <span className="animate-spin">⏳</span>}
				{t('AUTH.sign_in_title').toUpperCase()}
			</ThemedButton>
		</form>
	);
};

export { ClocLoginDialog, ClocUserAvatar, ClocLoginForm };
