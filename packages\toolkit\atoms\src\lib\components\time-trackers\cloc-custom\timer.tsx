'use client';

import React from 'react';
import { cn } from '@cloc/ui';
import Container from '../common/container';
import type { BasicTimerProps } from '../basic-timer/timer';
import TimerIcon from '../basic-timer/timer-icon';
import Clock from '../common/clock';
import TimerButton from '../basic-timer/timer-button';
import TimerTime from './timer-time';

interface clocCustomTimer extends BasicTimerProps {
	labeled?: boolean;
	labelClassName?: string;
}

const clocCustomTimer = ({
	ref,
	time,
	color,
	icon,
	iconClassName = 'text-black',
	buttonClassName,
	customIcon,
	customButtonIcon,
	isRunning,
	readonly,
	labeled,
	labelClassName = 'text-black',
	...props
}: clocCustomTimer & {
	ref?: React.RefObject<HTMLButtonElement>;
}) => {
	const { background, rounded, ...rest } = props;
	return (
		<Container ref={ref} {...rest}>
			{icon ? (
				<TimerIcon
					icon={<Clock />}
					{...(customIcon && { icon: customIcon })}
					className={cn(labeled && 'mb-3', iconClassName)}
					size={labeled ? 'extra-large' : 'large'}
				/>
			) : null}
			<TimerTime
				background={background}
				color={color}
				labelClassName={labelClassName}
				labeled={labeled}
				rounded={rounded}
				time={time}
			/>
			{!readonly && (
				<TimerButton
					{...(customButtonIcon && { icon: customButtonIcon })}
					className={cn(labeled && 'mb-3', buttonClassName)}
					isRunning={isRunning}
					size={labeled ? 'extra-large' : 'large'}
					variant="primary"
				/>
			)}
		</Container>
	);
};

clocCustomTimer.displayName = 'clocBasicTimer';
export default clocCustomTimer;
