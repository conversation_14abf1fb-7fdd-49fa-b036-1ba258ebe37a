{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "extends": "./base.json", "compilerOptions": {"plugins": [{"name": "next"}], "allowJs": true, "declaration": true, "declarationMap": false, "incremental": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "resolveJsonModule": true, "strict": true, "target": "es5"}, "include": ["src", "next-env.d.ts"], "exclude": ["node_modules"]}