// Polyfills for Node.js modules in browser environment
// This file is loaded by Docusaurus to provide necessary polyfills

// Polyfill for process
if (typeof global === 'undefined') {
  global = globalThis;
}

if (typeof process === 'undefined') {
  global.process = require('process/browser');
}

// Polyfill for Buffer
if (typeof Buffer === 'undefined') {
  global.Buffer = require('buffer').Buffer;
}

// Additional polyfills that might be needed
if (typeof setImmediate === 'undefined') {
  global.setImmediate = (fn, ...args) => setTimeout(fn, 0, ...args);
  global.clearImmediate = clearTimeout;
}
