/** @jsxImportSource theme-ui */
'use client';

import type { BasicTimerProps } from './timer';
import BaseAtom from './timer';
import { useClocContext } from '@lib/context/cloc-context';

interface BasicProps extends Omit<BasicTimerProps, 'time' | 'progress'> {
	progress?: boolean;
}

// TODO: Must move this to Atoms and integrate with gauzy api
export function BasicTimer({ progress, ...props }: BasicProps) {
	const {
		todayTrackedTime: { hours, minutes, seconds, totalSeconds },
		startTimer,
		stopTimer,
		isRunning,
		timerLoading,
		authenticatedUser
	} = useClocContext();

	return (
		<BaseAtom
			onClick={isRunning ? stopTimer : startTimer}
			{...props}
			isRunning={isRunning}
			{...(progress && { progress: (seconds / 100) * 60 })}
			time={{
				hours,
				minutes,
				seconds,
				totalSeconds
			}}
			disabled={timerLoading || !authenticatedUser}
		/>
	);
}

export function DefaultBasicTimer() {
	return <BasicTimer readonly />;
}

export function BasicTimerBorder() {
	return <BasicTimer border="thick" readonly />;
}

export function BasicTimerBorderRounded() {
	return <BasicTimer border="thick" readonly rounded="small" />;
}

export function BasicTimerBorderFullRounded() {
	return <BasicTimer border="thick" readonly rounded="large" />;
}

export function BasicTimerGray() {
	return <BasicTimer background="secondary" readonly />;
}

export function BasicTimerGrayRounded() {
	return <BasicTimer background="secondary" readonly rounded="small" />;
}

export function BasicTimerGrayFullRounded() {
	return <BasicTimer background="secondary" readonly rounded="large" />;
}

export function BasicTimerContained() {
	return <BasicTimer sx={{ background: 'mainColor' }} background="primary" color="destructive" readonly />;
}

export function BasicTimerContainedRounded() {
	return (
		<BasicTimer
			background="primary"
			color="destructive"
			readonly
			sx={{ background: 'mainColor' }}
			rounded="small"
		/>
	);
}

export function BasicTimerContainedFullRounded() {
	return <BasicTimer background="primary" color="destructive" readonly rounded="large" />;
}

export function BasicTimerIcon() {
	return <BasicTimer icon readonly />;
}
export function BasicTimerIconBorder() {
	return <BasicTimer border="thick" icon readonly />;
}

export function BasicTimerIconBorderRounded() {
	return <BasicTimer border="thick" icon readonly rounded="small" />;
}

export function BasicTimerIconBorderFullRounded() {
	return <BasicTimer border="thick" icon readonly rounded="large" />;
}

export function BasicTimerIconGray() {
	return <BasicTimer background="secondary" icon readonly />;
}

export function BasicTimerIconGrayRounded() {
	return <BasicTimer background="secondary" icon readonly rounded="small" />;
}

export function BasicTimerIconGrayFullRounded() {
	return <BasicTimer background="secondary" icon readonly rounded="large" />;
}

export function BasicTimerIconContained() {
	return <BasicTimer background="primary" color="destructive" icon readonly />;
}

export function BasicTimerIconContainedRounded() {
	return <BasicTimer background="primary" color="destructive" icon readonly rounded="small" />;
}

export function BasicTimerIconContainedFullRounded() {
	return <BasicTimer background="primary" color="destructive" icon readonly rounded="large" />;
}

export function BasicTimerIconProgress() {
	return <BasicTimer icon progress readonly />;
}
export function BasicTimerIconBorderProgress() {
	return <BasicTimer border="thick" icon progress readonly />;
}

export function BasicTimerIconBorderRoundedProgress() {
	return <BasicTimer border="thick" icon progress readonly rounded="small" />;
}

export function BasicTimerIconBorderFullRoundedProgress() {
	return <BasicTimer border="thick" icon progress readonly rounded="large" />;
}

export function BasicTimerIconGrayProgress() {
	return <BasicTimer background="secondary" icon progress readonly />;
}

export function BasicTimerIconGrayRoundedProgress() {
	return <BasicTimer background="secondary" icon progress readonly rounded="small" />;
}

export function BasicTimerIconGrayFullRoundedProgress() {
	return <BasicTimer background="secondary" icon progress readonly rounded="large" />;
}

export function BasicTimerIconContainedProgress() {
	return <BasicTimer background="primary" color="destructive" icon progress readonly />;
}

export function BasicTimerIconContainedRoundedProgress() {
	return <BasicTimer background="primary" color="destructive" icon progress readonly rounded="small" />;
}

export function BasicTimerIconContainedFullRoundedProgress() {
	return <BasicTimer background="primary" color="destructive" icon progress readonly rounded="large" />;
}

export function BasicTimerIconProgressButton() {
	return <BasicTimer icon progress />;
}
export function BasicTimerIconBorderProgressButton() {
	return <BasicTimer border="thick" icon progress />;
}

export function BasicTimerIconBorderRoundedProgressButton() {
	return <BasicTimer border="thick" icon progress rounded="small" />;
}

export function BasicTimerIconBorderFullRoundedProgressButton() {
	return <BasicTimer border="thick" icon progress rounded="large" />;
}

export function BasicTimerIconGrayProgressButton() {
	return <BasicTimer background="secondary" icon progress />;
}

export function BasicTimerIconGrayRoundedProgressButton() {
	return <BasicTimer background="secondary" icon progress rounded="small" />;
}

export function BasicTimerIconGrayFullRoundedProgressButton() {
	return <BasicTimer background="secondary" icon progress rounded="large" />;
}

export function BasicTimerIconContainedProgressButton() {
	return <BasicTimer background="primary" color="destructive" icon progress />;
}

export function BasicTimerIconContainedRoundedProgressButton() {
	return <BasicTimer background="primary" color="destructive" icon progress rounded="small" />;
}

export function BasicTimerIconContainedFullRoundedProgressButton() {
	return <BasicTimer background="primary" color="destructive" icon progress rounded="large" />;
}
