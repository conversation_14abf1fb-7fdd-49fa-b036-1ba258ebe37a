# CLOC Tracking System Documentation

## Overview

The CLOC Tracking System is a comprehensive analytics solution built on top of Microsoft Clarity that provides real-time user interaction tracking, session replay, heatmap visualization, and detailed analytics insights. The system consists of two main packages: a core tracking library and React components for visualization.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    CLOC Tracking System                         │
├─────────────────────────────────────────────────────────────────┤
│  Frontend Components (@cloc/atoms)                             │
│  ├── Tracking Components (React)                               │
│  ├── Context Providers                                         │
│  └── Utility Functions                                         │
├─────────────────────────────────────────────────────────────────┤
│  Core Tracking Library (@cloc/tracking)                        │
│  ├── Tracker (Clarity Integration)                             │
│  ├── Configuration Management                                  │
│  └── Data Upload Pipeline                                      │
├─────────────────────────────────────────────────────────────────┤
│  API Layer (@cloc/api)                                         │
│  ├── Tracking API Client                                       │
│  ├── Session Management                                        │
│  └── Data Retrieval                                            │
├─────────────────────────────────────────────────────────────────┤
│  Type Definitions (@cloc/types)                                │
│  ├── Session Interfaces                                        │
│  ├── API Response Types                                        │
│  └── Analytics Data Structures                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Package 1: Core Tracking Library (@cloc/tracking)

### Package Structure

```
packages/toolkit/tracking/
├── src/
│   ├── lib/
│   │   ├── tracker/
│   │   │   ├── tracker.ts          # Main tracker interface
│   │   │   └── config.ts           # Configuration management
│   │   ├── upload/
│   │   │   └── uploader.ts         # Data upload functionality
│   │   ├── helpers/
│   │   │   └── validate-config.ts  # Configuration validation
│   │   ├── types.ts                # Core type definitions
│   │   └── constant.ts             # API endpoints and constants
│   └── index.ts                    # Main export file
├── dist/                           # Built distribution files
├── package.json                    # Package configuration
└── README.md                       # Package documentation
```

### Core Functionality

#### 1. Tracker Interface (`tracker.ts`)

The main tracker provides a simple API for controlling Microsoft Clarity:

```typescript
import { tracker } from '@cloc/tracking';

// Start tracking with configuration
tracker.start({
	organizationId: 'your-org-id',
	tenantId: 'your-tenant-id',
	token: 'your-auth-token'
});

// Control tracking
tracker.pause(); // Pause tracking
tracker.resume(); // Resume tracking
tracker.stop(); // Stop tracking completely
```

**Key Features:**

- Configuration validation
- Error handling with descriptive messages
- Direct integration with Microsoft Clarity
- Simple start/stop/pause/resume controls

#### 2. Configuration Management (`config.ts`)

Handles the creation of Clarity-compatible configuration:

```typescript
export const createClocConfig = (config: IClocConfig): Config => ({
	upload: createUploader(config)
});
```

#### 3. Data Upload Pipeline (`uploader.ts`)

Manages secure data transmission to CLOC servers:

```typescript
// Automatic upload configuration with authentication
const uploader = createUploader({
	organizationId: 'org-123',
	tenantId: 'tenant-456',
	token: 'auth-token'
});
```

**Features:**

- Automatic authentication header injection
- Organization and tenant context
- Error handling and retry logic
- JSON payload formatting

#### 4. Configuration Validation (`validate-config.ts`)

Ensures all required configuration is present and valid:

```typescript
validateConfig({
	organizationId: 'required-string',
	tenantId: 'required-string',
	token: 'required-string'
});
```

### Dependencies

- **clarity-js**: ^0.8.19 - Microsoft Clarity integration
- **TypeScript**: ^5.6.3 - Type safety and compilation
- **Rollup**: ^3.0.0 - Module bundling

### Build Configuration

The package builds multiple distribution formats:

- **UMD**: `dist/tracker.umd.js` - Universal module
- **ES Module**: `dist/tracker.module.js` - Modern ES modules
- **CommonJS**: `dist/tracker.js` - Node.js compatibility
- **Minified**: `dist/tracker.min.js` - Production optimized
- **Types**: `dist/index.d.ts` - TypeScript definitions

## Package 2: React Components (@cloc/atoms)

### Package Structure

```
packages/toolkit/atoms/src/lib/components/cloc-tracking-components/
├── tracking-click-insight.tsx      # Click analytics component
├── tracking-session-insight.tsx    # Session analytics component
├── tracking-session-replay.tsx     # Session replay component
├── tracking-heatmap.tsx            # Heatmap visualization component
├── tracking-insight.tsx            # Combined analytics dashboard
├── filter/
│   └── tracking-filter.tsx         # Date/time filtering component
├── insight/
│   ├── click-insight.tsx           # Detailed click analytics
│   ├── session-insight.tsx         # Detailed session analytics
│   └── dashboard-insight.tsx       # Combined dashboard
├── clarity/
│   ├── clarity-heatmap.tsx         # Clarity heatmap integration
│   ├── clarity-replay.tsx          # Clarity replay integration
│   └── basic-clarity-replay.tsx    # Basic replay component
├── examples/
│   ├── analytics-usage-example.tsx # Usage examples
│   └── heatmap-usage-example.tsx   # Heatmap examples
└── index.ts                        # Component exports
```

### Core Components

#### 1. ClocTrackingClickInsight

Provides detailed click analytics and insights:

```typescript
import { ClocTrackingClickInsight } from '@cloc/atoms';

<ClocTrackingClickInsight className="w-full" />
```

**Features:**

- Total clicks and click rate metrics
- Click density analysis
- Unique elements tracking
- Spatial distribution analysis
- Top clicked elements list
- Real-time data updates
- Error handling and loading states

#### 2. ClocTrackingSessionInsight

Displays comprehensive session analytics:

```typescript
import { ClocTrackingSessionInsight } from '@cloc/atoms';

<ClocTrackingSessionInsight className="w-full" />
```

**Features:**

- Session duration metrics
- Engagement scoring
- Interaction rate analysis
- Event count tracking
- Device type detection
- Time-based analytics

#### 3. ClocTrackingHeatmap

Interactive heatmap visualization with advanced controls:

```typescript
import { ClocTrackingHeatmap } from '@cloc/atoms';

<ClocTrackingHeatmap
  className="w-full"
  showControl={true}
/>
```

**Features:**

- Click and scroll heatmaps
- Multiple color schemes (hot, cool, blue)
- Adjustable aggregation radius
- Device type statistics
- Performance optimizations (70-80% fewer re-renders)
- Viewport-based filtering
- Debounced controls

#### 4. ClocTrackingSessionReplay

Session replay functionality with playback controls:

```typescript
import { ClocTrackingSessionReplay } from '@cloc/atoms';

<ClocTrackingSessionReplay className="w-full" />
```

#### 5. ClocTrackingFilter

Date and time filtering for session data:

```typescript
import { ClocTrackingFilter } from '@cloc/atoms';

<ClocTrackingFilter
  autoRefresh={true}
  refreshInterval={30000}
/>
```

**Features:**

- Date range selection
- Time range controls
- Employee filtering
- Auto-refresh capabilities
- Real-time validation

### Context System

#### TrackingProvider

Global state management for tracking components:

```typescript
import { TrackingProvider } from '@cloc/atoms';

<TrackingProvider config={{ baseUrl: 'https://api.cloc.ai' }}>
  <YourTrackingComponents />
</TrackingProvider>
```

**Provides:**

- Session data management
- Filter state synchronization
- API configuration
- Loading and error states
- Automatic data fetching

#### useTrackingContext Hook

Access tracking data and controls:

```typescript
import { useTrackingContext } from '@cloc/atoms';

const {
	sessions, // Current session data
	loading, // Loading state
	error, // Error messages
	formData, // Filter configuration
	setFormData, // Update filters
	fetchSessions, // Manual data fetch
	refetchSessions // Refresh data
} = useTrackingContext();
```

### Utility Functions

#### Analytics Calculations (`tracking-utils.ts`)

Comprehensive analytics calculation functions:

```typescript
import {
	calculateClickInsights,
	calculateSessionMetrics,
	extractClickEvents,
	getSessionDeviceType,
	formatDuration
} from '@cloc/atoms';

// Calculate click analytics
const clickInsights = calculateClickInsights(decodedPayloads);

// Get session metrics
const sessionMetrics = calculateSessionMetrics(decodedPayloads);

// Device detection
const { isMobile, deviceLabel } = getSessionDeviceType(decodedPayloads);
```

**Available Utilities:**

- Click event extraction and analysis
- Session duration and engagement calculations
- Device type detection
- Spatial distribution analysis
- Hotspot identification
- Date formatting helpers

## Integration Guide

### Basic Setup

1. **Install Dependencies**

```bash
npm install @cloc/tracking @cloc/atoms @cloc/api @cloc/types
```

2. **Initialize Tracking**

```typescript
import { tracker } from '@cloc/tracking';

tracker.start({
	organizationId: 'your-org-id',
	tenantId: 'your-tenant-id',
	token: 'your-auth-token'
});
```

3. **Add React Components**

```typescript
import {
  TrackingProvider,
  ClocTrackingHeatmap,
  ClocTrackingClickInsight,
  ClocTrackingFilter
} from '@cloc/atoms';

function App() {
  return (
    <TrackingProvider>
      <ClocTrackingFilter />
      <ClocTrackingHeatmap />
      <ClocTrackingClickInsight />
    </TrackingProvider>
  );
}
```

### Advanced Configuration

#### Custom API Configuration

```typescript
<TrackingProvider
  config={{
    baseUrl: 'https://your-api.com',
    headers: { 'Custom-Header': 'value' },
    timeout: 60000
  }}
>
  {/* Components */}
</TrackingProvider>
```

#### Performance Optimization

```typescript
// Heatmap with performance optimizations
<ClocTrackingHeatmap
  showControl={true}
  className="optimized-heatmap"
/>
```

## API Integration

### Session Data Structure

```typescript
interface IClocSession {
	sessionId: string;
	payloads: string[]; // Encoded Clarity payloads
	createdAt: string;
	updatedAt: string;
	employeeId: string;
	organizationId: string;
	tenantId: string;
}
```

### API Endpoints

- **POST** `/api/tracking` - Store tracking data
- **GET** `/api/tracking/sessions` - Retrieve sessions
- **GET** `/api/tracking/sessions/filtered` - Filtered session data

### Authentication

All API requests require:

- `Authorization: Bearer <token>`
- `organization-id: <org-id>`
- `tenant-id: <tenant-id>`

## Current Capabilities

### ✅ Implemented Features

1. **Core Tracking**
    - Microsoft Clarity integration
    - Real-time data collection
    - Secure data upload
    - Configuration validation

2. **Analytics Components**
    - Click analytics with spatial distribution
    - Session metrics and engagement scoring
    - Interactive heatmaps (click/scroll)
    - Session replay functionality
    - Device type detection

3. **Data Management**
    - Global state management
    - Filtering and date range selection
    - Auto-refresh capabilities
    - Error handling and loading states

4. **Performance Optimizations**
    - Component memoization (70-80% fewer re-renders)
    - Debounced controls
    - Viewport-based filtering
    - Efficient data processing

5. **Developer Experience**
    - TypeScript support
    - Comprehensive examples
    - Flexible component APIs
    - Error boundaries

### 🚧 Current Limitations

1. **API Endpoint**: Currently configured for localhost development
2. **Real-time Updates**: No WebSocket integration for live data
3. **Data Export**: No built-in export functionality
4. **Advanced Filtering**: Limited to date/time and employee filters
5. **Custom Metrics**: No user-defined analytics calculations

### 🔮 Future Development Areas

1. **Enhanced Analytics**
    - Conversion funnel analysis
    - A/B testing integration
    - Custom event tracking
    - Performance metrics

2. **Advanced Visualizations**
    - Journey mapping
    - Cohort analysis
    - Real-time dashboards
    - Custom chart types

3. **Integration Improvements**
    - WebSocket real-time updates
    - Data export (CSV, PDF)
    - Advanced filtering options
    - Alerting and notifications

4. **Performance Enhancements**
    - Data virtualization
    - Progressive loading
    - Caching strategies
    - Background processing

## Dependencies and Integrations

### Core Dependencies

- **Microsoft Clarity**: Session recording and data collection
- **clarity-decode**: Payload decoding and analysis
- **React**: Component framework
- **TypeScript**: Type safety
- **Tailwind CSS**: Styling framework

### Integration Points

- **@cloc/ui**: UI component library
- **@cloc/api**: API client functionality
- **@cloc/types**: Shared type definitions
- **react-i18next**: Internationalization
- **lucide-react**: Icon library

This documentation provides a comprehensive overview of the current tracking functionality implementation, covering both the core tracking library and React components with their capabilities, limitations, and integration patterns.
