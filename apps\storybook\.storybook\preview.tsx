import type { Preview } from '@storybook/react';
import { ClocProvider, ClocLoginDialog } from '@cloc/atoms';
import './style.css';
import { Decorator } from '@storybook/react';
import { Toaster } from '@cloc/ui';

const CLOC_API_URL = process.env.STORYBOOK_CLOC_API_URL || 'https://api.ever.team/api';

const ClocDecorator: Decorator = (Story, context) => {
	return (
		<ClocProvider config={{ apiUrl: CLOC_API_URL }}>
			<div className="fixed dark:text-white left-0  z-[49]  top-0  flex justify-between gap-4  p-4  items-center">
				<ClocLoginDialog />
			</div>

			<Story {...context} />
			<Toaster />
		</ClocProvider>
	);
};

export const decorators = [ClocDecorator];

const preview: Preview = {
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i
			}
		}
	}
};

export default preview;
