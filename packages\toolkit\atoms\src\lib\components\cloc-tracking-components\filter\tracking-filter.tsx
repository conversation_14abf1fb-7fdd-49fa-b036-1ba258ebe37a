'use client';

import React, { useEffect } from 'react';
import {
	Accordion,
	AccordionItem,
	AccordionTrigger,
	AccordionContent,
	Input,
	ThemedButton,
	DatePicker,
	Button,
	cn
} from '@cloc/ui';
import { Building2, Calendar, ListFilter, Loader2, TimerIcon, Users, Info, Download, RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useTrackingContext } from '@lib/context/cloc-tracking-context';
import { ClocActiveEmployeeSelector } from '@components/cloc-ui-components/inputs/cloc-active-employee-selector';
import { ClocActiveOrganizationSelector } from '@components/cloc-ui-components/inputs/cloc-active-organization-selector';
/**
 * Global Tracking Filter Component
 * Provides unified filter controls for tracking components
 */
interface ITrackingFilterProps {
	className?: string;
	autoRefresh?: boolean;
	refreshInterval?: number;
}

export const ClocTrackingFilter: React.FC<ITrackingFilterProps> = ({
	className = '',
	autoRefresh = false,
	refreshInterval = 30000 // 30 seconds
}) => {
	const { t } = useTranslation(undefined, { keyPrefix: 'CLOC_TRACKING_FILTER' });
	const { formData, setFormData, sessions, loading, error, fetchSessions } = useTrackingContext();

	const handleDateChange = (newDate: Date | undefined) => {
		if (!newDate) return;
		const date = newDate as Date;
		setFormData((prev) => ({
			...prev,
			from: `${date.toISOString().split('T')[0]}T${prev.from.split('T')[1]}`
		}));
	};

	const handleFromTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFormData((prev) => ({
			...prev,
			from: `${prev.from.split('T')[0]}T${e.target.value}:00.000Z`
		}));
	};

	const handleToTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFormData((prev) => ({
			...prev,
			to: `${prev.to.split('T')[0]}T${e.target.value}:00.000Z`
		}));
	};

	// Manual refresh handler
	const handleManualRefresh = async () => {
		try {
			await fetchSessions();
		} catch (error) {
			console.error('Manual refresh failed:', error);
		}
	};

	// Export analytics data (placeholder for future implementation)
	const handleExportData = () => {
		// TODO: Implement data export functionality
	};

	// Auto-refresh functionality
	useEffect(() => {
		if (!autoRefresh) return;

		const interval = setInterval(async () => {
			try {
				await fetchSessions();
			} catch (error) {
				console.error('Auto-refresh failed:', error);
			}
		}, refreshInterval);

		return () => clearInterval(interval);
	}, [autoRefresh, refreshInterval, fetchSessions]);

	return (
		<div
			className={`mb-4 rounded-xl border border-gray-200 dark:border-gray-700 p-5 bg-white dark:bg-black ${className}`}
		>
			<Accordion className="w-full text-sm mb-4 rounded-lg" type="multiple">
				<AccordionItem value="filter">
					<AccordionTrigger className="py-0">
						<div className="flex gap-2 justify-center items-center font-semibold text-gray-900 dark:text-white">
							<ListFilter size={15} className="text-gray-400" />
							<h2>{t('title')}</h2>
						</div>
					</AccordionTrigger>
					<AccordionContent className="p-0 w-full">
						<div className="flex flex-col lg:flex-row w-full gap-4 my-3">
							<div className="w-full">
								<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									<Building2 className="inline h-4 w-4 mr-1" />
									{t('labels.organization')}
								</label>
								<ClocActiveOrganizationSelector />
							</div>

							<div className="w-full">
								<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									<Users className="inline h-4 w-4 mr-1" />
									{t('labels.employee')}
								</label>
								<ClocActiveEmployeeSelector className="w-full" />
							</div>

							<div className="w-full">
								<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									<Calendar className="inline h-4 w-4 mr-1" />
									{t('labels.date')}
								</label>
								<DatePicker
									date={new Date(formData.from.split('T')[0])}
									setDate={(newDate) => {
										if (!newDate) return;
										const date = newDate as Date;
										handleDateChange(date);
									}}
									className="w-full"
								/>
							</div>

							<div className="w-full">
								<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									<TimerIcon className="inline h-4 w-4 mr-1" />
									{t('labels.time_range')}
								</label>
								<div className="flex w-full gap-2">
									<Input
										type="time"
										value={formData.from.split('T')[1].slice(0, 5)}
										onChange={handleFromTimeChange}
										className="w-full"
									/>
									<Input
										type="time"
										value={formData.to.split('T')[1].slice(0, 5)}
										onChange={handleToTimeChange}
										className="w-full"
									/>
								</div>
							</div>
						</div>
						<ThemedButton onClick={fetchSessions} disabled={loading}>
							{loading && <Loader2 className="animate-spin h-4 w-4 mr-2" />} {t('actions.apply_filter')}
						</ThemedButton>
					</AccordionContent>
				</AccordionItem>
			</Accordion>

			{/* Results Summary */}
			<div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Info size={14} className="text-gray-400" />
						<span className="text-xs text-gray-600 dark:text-gray-400">Results:</span>
					</div>

					<div className="flex items-center gap-2">
						{loading && <Loader2 className="animate-spin h-3 w-3 text-gray-400" />}
						<span className="text-xs font-semibold text-gray-900 dark:text-white" aria-live="polite">
							{loading
								? t('results.loading')
								: error
									? t('results.error_occurred')
									: !sessions || sessions.length === 0
										? t('results.no_session_found')
										: sessions.length === 1
											? t('results.sessions_found', { count: sessions.length })
											: t('results.sessions_found_plural', { count: sessions.length })}
						</span>

						{/* Auto-refresh indicator */}
						{autoRefresh && (
							<div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">
								<div
									className={cn(
										'w-2 h-2 rounded-full',
										loading ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'
									)}
								/>
								{t('results.auto_refresh')}
							</div>
						)}

						{/* Manual refresh button */}
						<Button onClick={handleManualRefresh} variant="outline" size="sm" disabled={loading}>
							{loading ? <Loader2 size={16} className="animate-spin" /> : <RefreshCw size={16} />}
							{t('actions.refresh')}
						</Button>

						{/* Export button */}
						<Button onClick={handleExportData} variant="outline" size="sm">
							<Download size={16} />
							{t('actions.export')}
						</Button>
					</div>
				</div>
				{error && (
					<div
						className="mt-2 text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded border border-red-200 dark:border-red-800"
						role="alert"
					>
						{error}
					</div>
				)}
			</div>
		</div>
	);
};

export default ClocTrackingFilter;
