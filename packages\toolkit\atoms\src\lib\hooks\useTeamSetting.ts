import { getOrganisationTeams, updateTeam, uploadFile } from '@cloc/api';
import { IUser } from '@cloc/types';
import { toast } from '@cloc/ui';
import { useAccessToken } from '@hooks/useAccessToken';
import { organizationTeamsAtom } from '@lib/cloc-jotai/atoms/cloc-atoms';
import { useClocContext } from '@lib/context/cloc-context';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface FormValues {
	imageId?: string;
	name?: string;
	color?: string;
	size?: string;
	isPublic?: boolean;
}

const defaultFormValues: FormValues = {
	imageId: '',
	name: '',
	color: '',
	size: '',
	isPublic: false
};

export const useTeamSetting = (user: IUser | null, organizationId: string, selectedTeam: string) => {
	const [formValues, setFormValues] = useState<FormValues>(defaultFormValues);

	const { organizationTeams } = useClocContext();
	const { accessToken: token } = useAccessToken();
	const [error, setError] = useState<string>('');
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [file, setFile] = useState<File | null>(null);
	const [preview, setPreview] = useState<string>('');

	const [, setTeams] = useAtom(organizationTeamsAtom);

	const { t } = useTranslation(undefined, { keyPrefix: 'TEAM_SETTING.dialog' });

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFormValues((prev) => ({
			...prev,
			[e.target.name]: e.target.value
		}));
		setError(''); // Clear error on change
	};

	const handleRadioChange = (e: string) => {
		setFormValues((prev) => ({
			...prev,
			isPublic: e === 'public' ? true : false
		}));
		setError(''); // Clear error on change
	};

	const handleSelectChange = (e: string) => {
		setFormValues((prev) => ({
			...prev,
			size: e
		}));
		setError(''); // Clear error on change
	};

	const handleInputFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		setError('');
		const { files } = e.target;
		if (files && files.length > 0) {
			setFile(files[0]);
		}
	};

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		setError(''); // Clear previous error
		setIsSubmitting(true);

		// API Calls

		try {
			if (selectedTeam === 'all') throw Error('Please select a team to update');

			let imageId = '';

			if (file) {
				const uploadedFile = await uploadFile({ file, token, user, folderName: 'team_avatars' });

				if ('message' in uploadedFile || 'error' in uploadedFile) {
					const errorMessage =
						'message' in uploadedFile
							? Array.isArray(uploadedFile.message)
								? uploadedFile.message.join(', ')
								: uploadedFile.message
							: String(uploadedFile.error);

					setError(errorMessage);
					toast({
						variant: 'destructive',
						description: errorMessage
					});

					return;
				}

				imageId = uploadedFile.id;
			}

			const updatedTeam = await updateTeam({
				currentUser: user,
				organizationId,
				token,
				data: {
					name: formValues.name,
					teamId: selectedTeam,
					color: formValues.color,
					public: formValues.isPublic,
					teamSize: formValues.size,
					imageId
				}
			});

			if ('message' in updatedTeam || 'error' in updatedTeam) {
				const errorMessage =
					'message' in updatedTeam
						? Array.isArray(updatedTeam.message)
							? updatedTeam.message.join(', ')
							: updatedTeam.message
						: String(updatedTeam.error);

				setError(errorMessage);
				toast({
					variant: 'destructive',
					description: errorMessage
				});

				return;
			}
			// Fetch updated teams
			setTeams((prev) => ({ ...prev, loading: true }));
			const userTeams = await getOrganisationTeams(user, token, organizationId);

			setTeams((prev) => {
				if (userTeams && !('error' in userTeams || 'message' in userTeams)) {
					return { data: userTeams, loading: false };
				}
				return { ...prev, loading: false };
			});

			toast({ description: t('success'), variant: 'default' });
		} catch (error) {
			setError((error as Error)?.message || t('failure'));
			toast({
				variant: 'destructive',
				description: (error as Error)?.message || t('failure')
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	useEffect(() => {
		if (file) {
			const newPreview = URL.createObjectURL(file);
			setPreview(newPreview);
			return () => {
				URL.revokeObjectURL(newPreview);
			};
		}
	}, [file]);

	useEffect(() => {
		if (selectedTeam && selectedTeam !== 'all') {
			const team = organizationTeams?.items.find((elt) => elt.id === selectedTeam);
			if (team) {
				setFormValues((prev) => ({
					...prev,
					name: team.name,
					color: team.color,
					size: team.teamSize,
					isPublic: team.public
				}));
				setPreview(team.logo || '');
			}
		} else {
			setFormValues(defaultFormValues);
		}
	}, [selectedTeam]);

	return {
		error,
		formValues,
		preview,
		handleChange,
		handleRadioChange,
		handleSelectChange,
		handleInputFileChange,
		handleSubmit,
		isSubmitting
	};
};
