import { addTimeSlot, startTimer as startApiTimer, stopTimer as stopApiTimer } from '@cloc/api';
import { useEffect, useRef } from 'react';
import { ITimerStatus, IUser } from '@cloc/types';
import { useClocStopWatch } from './useClocStopWatch';
import { ICurrentClocState } from '@cloc/types';
import { toast } from '@cloc/ui';
import { useAtom } from 'jotai';
import { timerLoadingAtom } from '../cloc-jotai/atoms/cloc-atoms';

export interface IUseTimerArguments {
	token: string;
	currentClocState: ICurrentClocState;
	user: IUser | null;
	timerStatus: ITimerStatus;
	organizationId?: string;
}

export const useTimer = ({ user, token, currentClocState, timerStatus, organizationId }: IUseTimerArguments) => {
	const {
		todayTrackedTime,
		setTodayTrackedTime,
		start,
		pause,
		reset,
		isRunning,
		hours,
		minutes,
		seconds,
		totalSeconds,
		time,
		setTime
	} = useClocStopWatch(timerStatus);

	const [, setTimerLoading] = useAtom(timerLoadingAtom);

	const interval = useRef<number>(0);

	const startTimer = async () => {
		try {
			setTimerLoading(true);
			const timeSlot = await startApiTimer(user, token, currentClocState, organizationId);

			if ('message' in timeSlot || 'error' in timeSlot) {
				const errorMessage =
					'message' in timeSlot
						? Array.isArray(timeSlot.message)
							? timeSlot.message.join(', ')
							: timeSlot.message
						: String(timeSlot.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});

				return;
			}

			start();

			// interval.current = setInterval(async () => {
			// 	await addTimeSlot({
			// 		duration: timeSlotTime,
			// 		logType: 'TRACKED',
			// 		organizationId: user?.employee?.organizationId,
			// 		source: 'CLOC',
			// 		tenantId: user?.tenantId
			// 	});
			// }, timeSlotTime * 1000);
		} catch (error) {
			toast({
				title: 'Ever Cloc Error',
				description: (error as Error).message,
				variant: 'destructive'
			});
		} finally {
			setTimerLoading(false);
		}
	};

	const stopTimer = async () => {
		clearInterval(interval.current);
		try {
			setTimerLoading(true);
			const timeSlot = await stopApiTimer(user, token, organizationId);

			if ('message' in timeSlot || 'error' in timeSlot) {
				const errorMessage =
					'message' in timeSlot
						? Array.isArray(timeSlot.message)
							? timeSlot.message.join(', ')
							: timeSlot.message
						: String(timeSlot.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});

				return;
			}

			// const time = timeSlot?.duration! > timeSlotTime ? timeSlot?.duration! % timeSlotTime : timeSlot?.duration!;

			timeSlot && (await addTimeSlot(token, timeSlot));

			reset();
		} catch (error) {
			toast({
				title: 'Ever Cloc Error',
				description: (error as Error).message,
				variant: 'destructive'
			});
		} finally {
			setTimerLoading(false);
		}
	};

	useEffect(() => {
		if (timerStatus?.running) {
			setTime(new Date(timerStatus?.duration * 1000));
			start();
		}
	}, [timerStatus]);

	return {
		start,
		pause,
		startTimer,
		stopTimer,
		isRunning,
		hours,
		minutes,
		seconds,
		totalSeconds,
		timerStatus,
		time,
		setTime,
		todayTrackedTime,
		setTodayTrackedTime
	};
};
