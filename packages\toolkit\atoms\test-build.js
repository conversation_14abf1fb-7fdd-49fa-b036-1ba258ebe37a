#!/usr/bin/env node

/**
 * Test script to verify that the built package doesn't contain Node.js dependencies
 * that would cause issues in browser environments like Docusaurus
 */

const fs = require('fs');
const path = require('path');

const distPath = path.join(__dirname, 'dist', 'index.es.js');

if (!fs.existsSync(distPath)) {
  console.error('❌ Build file not found. Run `npm run build` first.');
  process.exit(1);
}

const buildContent = fs.readFileSync(distPath, 'utf8');

// Check for problematic Node.js imports
const problematicImports = [
  'import util from "util"',
  'import util from \'util\'',
  'import P from"util"', // Minified version
  'require("util")',
  'require(\'util\')',
  'from"util"',
  'from \'util\'',
  'from "util"'
];

let hasIssues = false;

console.log('🔍 Checking build output for Node.js dependencies...\n');

problematicImports.forEach(importPattern => {
  if (buildContent.includes(importPattern)) {
    console.error(`❌ Found problematic import: ${importPattern}`);
    hasIssues = true;
  }
});

// Check for other Node.js modules that might cause issues
const nodeModules = ['fs', 'path', 'crypto', 'stream', 'querystring', 'buffer', 'process'];
nodeModules.forEach(module => {
  const patterns = [
    `import ${module} from "${module}"`,
    `import ${module} from '${module}'`,
    `require("${module}")`,
    `require('${module}')`
  ];
  
  patterns.forEach(pattern => {
    if (buildContent.includes(pattern)) {
      console.error(`❌ Found Node.js module import: ${pattern}`);
      hasIssues = true;
    }
  });
});

// Check file size
const stats = fs.statSync(distPath);
const fileSizeInMB = stats.size / (1024 * 1024);

console.log(`📦 Build file size: ${fileSizeInMB.toFixed(2)} MB`);

if (fileSizeInMB > 5) {
  console.warn('⚠️  Build file is quite large. Consider reviewing bundled dependencies.');
}

if (!hasIssues) {
  console.log('✅ Build output looks good! No problematic Node.js dependencies found.');
  console.log('✅ The package should work correctly in browser environments like Docusaurus.');
} else {
  console.error('\n❌ Build contains problematic dependencies that may cause issues in browser environments.');
  console.error('💡 Consider updating the Rollup configuration to externalize these dependencies.');
  process.exit(1);
}

// Additional checks
console.log('\n🔍 Additional checks:');

// Check if universal export exists
const universalPath = path.join(__dirname, 'dist', 'universal.es.js');
if (fs.existsSync(universalPath)) {
  console.log('✅ Universal export found');
} else {
  console.warn('⚠️  Universal export not found');
}

// Check if CSS is generated
const cssPath = path.join(__dirname, 'dist', 'index.es.css');
if (fs.existsSync(cssPath)) {
  console.log('✅ CSS file generated');
} else {
  console.warn('⚠️  CSS file not found');
}

// Check if type definitions exist
const typesPath = path.join(__dirname, 'dist', 'index.d.ts');
if (fs.existsSync(typesPath)) {
  console.log('✅ Type definitions generated');
} else {
  console.warn('⚠️  Type definitions not found');
}

console.log('\n🎉 Build verification complete!');
