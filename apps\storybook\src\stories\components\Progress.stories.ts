import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Progress } from '@cloc/ui';

const meta = {
	title: 'Components/Progress',
	component: Progress,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof Progress>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultProgress: Story = {
	args: { value: 50, className: 'bg-black-400' }
};
