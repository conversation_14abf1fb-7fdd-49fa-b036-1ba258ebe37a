/** @jsxImportSource theme-ui */
'use client';

import React from 'react';
import { ExpandIcon } from '@cloc/ui';

import { cva, VariantProps } from 'class-variance-authority';
import { cn } from '@cloc/ui';

import { ISeparator } from '@cloc/types';

import { ClocButton } from '../../cloc-ui-components/button/button';
import { TimeDisplayer } from '../../cloc-ui-components/time-displayer';
import { TodayTimeDisplayer } from '../../cloc-ui-components/today-timer-displayer';
import { ClocProgress } from '../../cloc-ui-components/cloc-progress';
import { SpinOverlayLoader } from 'src/lib/components/loaders/spin-overlay-loader';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';
import { ClocTimerForm } from '@components/forms/cloc-timer-form';
import { ClocTimerFooter } from '@components/layouts/footers/component-footer';

const modernClocVariants = cva(
	'backdrop-blur-sm  p-6 bg-white dark:bg-black text-black dark:text-white relative  w-[500px] rounded-xl flex flex-col justify-start gap-5 shadow-2xl dark:shadow-white/10 custom-scroll ',
	{
		variants: {
			variant: {
				default: '',
				bordered: 'border-2 border-secondaryColor'
			},
			size: {
				default: 'w-[300px]',
				sm: ' w-[220px] text-sm p-4',
				lg: ' w-[500px] px-8'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	}
);

export interface IModernClocProps extends VariantProps<typeof modernClocVariants> {
	showProgress: boolean;
	separator?: ISeparator;
	expanded: boolean;
	className?: string;
	draggable?: boolean;
	resizable?: boolean;
	style?: React.CSSProperties | undefined;
}

function ModernCloc({
	variant,
	size,
	className,
	separator,
	showProgress = false,
	expanded = false,
	draggable = true,
	resizable = false,
	style,
	...props
}: IModernClocProps): React.JSX.Element {
	const {
		seconds,
		loadings: { userLoading, timerStatusLoading }
	} = useClocContext();

	const { t } = useTranslation();

	const [expandedState, setExpandedState] = React.useState<boolean>(expanded);

	return (
		// <Draggable disabled={!draggable} handle=".handle">
		<div
			style={style}
			className={cn(
				modernClocVariants({ variant, size, className }),
				'relative',
				size == 'sm' && 'gap-3 text-sm',
				resizable && 'min-w-[300px] max-w-[1000px] resize overflow-auto'
			)}
			sx={{
				color: 'textColor',
				backgroundColor: 'backgroundColor',
				borderColor: 'borderColor'
			}}
			{...props}
		>
			{draggable && <div className="handle w-full h-8 absolute top-0 left-0 cursor-grab"></div>}
			{(timerStatusLoading || userLoading) && <SpinOverlayLoader />}
			{expandedState && <h1 className="font-semibold text-black dark:text-white">{t('TIME_TRACKER.title')}</h1>}
			<div className={`flex justify-center items-center gap-2 `}>
				<div
					className={`absolute  flex gap-3 justify-center items-center top-3 right-3 ${size == 'sm' && 'top-1 '} `}
				>
					{
						<button className="text-black dark:text-white" onClick={() => setExpandedState(!expandedState)}>
							{expandedState ? '-' : <ExpandIcon size={10} />}
						</button>
					}
					{/* <button>
							<CloseIcon size={15} />
						</button> */}
				</div>
				{/* TODO : Connect to basic */}
				<ClocButton size={size == 'sm' ? 'sm' : 'default'} />

				<div className="flex flex-col gap-1  justify-center ">
					<div className={`font-semibold text-black dark:text-white ${size == 'sm' && 'text-xl'} text-3xl`}>
						<TimeDisplayer
							className="text-black dark:text-white text-4xl w-[200px] lg:text-start tracking-wide font-normal"
							fontSize={size == 'sm' ? 20 : 30}
							separator={separator}
						/>
					</div>
					{showProgress && (
						<>
							{size != 'sm' && (
								<div className="text-xs text-black dark:text-white font-thin pb-2 ">
									<span className="dark:text-gray-400 text-gray-700 font-thin">
										{t('COMMON.today')} :{' '}
									</span>{' '}
									<TodayTimeDisplayer className="font-normal" separator={separator} />
								</div>
							)}
							<ClocProgress className="h-2" value={(seconds / 60) * 100} />
						</>
					)}
				</div>
			</div>
			{expandedState && (
				<>
					<ClocTimerForm size={size} />
					<ClocTimerFooter />
				</>
			)}
		</div>
		// </Draggable>
	);
}

export { ModernCloc, modernClocVariants };
