import React from 'react';

const Play = function Play({
	ref,
	...props
}: React.SVGProps<SVGSVGElement> & {
	ref?: React.RefObject<SVGSVGElement>;
}) {
	return (
		<svg
			fill="none"
			height="60"
			viewBox="0 0 60 60"
			width="60"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
			ref={ref}
		>
			<path
				clipRule="evenodd"
				d="M29.7774 1.77344C14.309 1.77344 1.76953 14.313 1.76953 29.7813C1.76953 45.2496 14.309 57.7892 29.7774 57.7892C45.2457 57.7892 57.7853 45.2496 57.7853 29.7813C57.7853 14.313 45.2457 1.77344 29.7774 1.77344ZM3.76953 29.7813C3.76953 15.4176 15.4136 3.77344 29.7774 3.77344C44.1412 3.77344 55.7853 15.4176 55.7853 29.7813C55.7853 44.1451 44.1412 55.7892 29.7774 55.7892C15.4136 55.7892 3.76953 44.1451 3.76953 29.7813ZM54.4708 29.7762C54.4708 43.4136 43.4154 54.4691 29.7778 54.4691C16.1404 54.4691 5.08496 43.4136 5.08496 29.7762C5.08496 16.1386 16.1404 5.08323 29.7778 5.08323C43.4154 5.08323 54.4708 16.1386 54.4708 29.7762ZM28.0966 42.0373L28.8259 19.8283C28.8718 18.522 30.2774 17.6553 31.3398 18.2962L41.702 24.546C46.2229 27.2728 45.8704 34.2414 41.0639 37.1143L33.7773 41.4631L30.4616 43.4458C29.3496 44.0895 28.046 43.3212 28.0966 42.0373ZM19.9988 38.2993L20.3798 30.8141L20.7663 23.3501C20.9126 20.6773 22.5146 18.5182 24.605 17.5127C25.6123 17.015 26.7056 17.8082 26.6578 18.9419L25.8203 42.6482C25.7811 43.7675 24.6746 44.6056 23.6883 44.2197C21.4701 43.3587 19.8468 41.1598 19.9988 38.2993Z"
				fill="currentColor"
				fillRule="evenodd"
			/>
		</svg>
	);
};

export default Play;
