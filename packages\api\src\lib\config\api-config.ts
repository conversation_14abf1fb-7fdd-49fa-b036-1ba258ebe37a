export interface IApiClocConfig {
	apiUrl: string;
	apiVersion?: string;
	environment?: 'development' | 'production' | 'test';
}

class ApiConfigManager {
	private static instance: ApiConfigManager;
	private config: IApiClocConfig = { apiUrl: 'https://api.cloc.ai/api' };

	private constructor() {}

	static getInstance(): ApiConfigManager {
		if (!ApiConfigManager.instance) {
			ApiConfigManager.instance = new ApiConfigManager();
		}
		return ApiConfigManager.instance;
	}

	setConfig(config: Partial<IApiClocConfig>) {
		this.config = {
			...this.config,
			...config
		};
	}

	getConfig(): IApiClocConfig {
		return this.config;
	}
}

export const apiConfigManager = ApiConfigManager.getInstance();
