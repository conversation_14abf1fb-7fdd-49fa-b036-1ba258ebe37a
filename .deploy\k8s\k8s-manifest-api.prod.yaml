---
kind: Service
apiVersion: v1
metadata:
  name: ever-cloc-prod-api-lb
  annotations:
    service.beta.kubernetes.io/do-loadbalancer-name: "api.cloc.ai"
    service.beta.kubernetes.io/do-loadbalancer-protocol: "http2"
    service.beta.kubernetes.io/do-loadbalancer-http2-ports: "443"
    # Replace with your Certificate Id. You can get a list of Ids with 'doctl compute certificate list'
    service.beta.kubernetes.io/do-loadbalancer-certificate-id: "e9d49a9b-ea0b-48bc-bff1-b0465e77cd63"
    service.beta.kubernetes.io/do-loadbalancer-size-slug: "lb-small"
    service.beta.kubernetes.io/do-loadbalancer-hostname: "api.cloc.ai"
spec:
  type: LoadBalancer
  selector:
    app: ever-cloc-prod-api
  ports:
    - name: http
      protocol: TCP
      port: 443
      targetPort: 3000

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ever-cloc-prod-api
spec:
  replicas: 6
  selector:
    matchLabels:
      app: ever-cloc-prod-api
  template:
    metadata:
      labels:
        app: ever-cloc-prod-api
    spec:
      containers:
        - name: ever-cloc-prod-api
          image: registry.digitalocean.com/ever/gauzy-api:latest
          resources:
            requests:
              memory: "1536Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
          env:
            - name: API_HOST
              value: 0.0.0.0
            - name: DEMO
              value: "false"
            - name: NODE_ENV
              value: "production"
            - name: ADMIN_PASSWORD_RESET
              value: "true"
            - name: LOG_LEVEL
              value: "info"
            - name: CLOUD_PROVIDER
              value: "$CLOUD_PROVIDER"
            - name: SENTRY_DSN
              value: "$SENTRY_DSN"
            - name: SENTRY_HTTP_TRACING_ENABLED
              value: "$SENTRY_HTTP_TRACING_ENABLED"
            - name: SENTRY_PROFILING_ENABLED
              value: "$SENTRY_PROFILING_ENABLED"
            - name: SENTRY_POSTGRES_TRACKING_ENABLED
              value: "$SENTRY_POSTGRES_TRACKING_ENABLED"
            - name: API_BASE_URL
              value: "https://api.cloc.ai"
            - name: CLIENT_BASE_URL
              value: "https://app.cloc.ai"
            - name: DB_URI
              value: "$DB_URI"
            - name: DB_HOST
              value: "$DB_HOST"
            - name: DB_SSL_MODE
              value: "$DB_SSL_MODE"
            - name: DB_CA_CERT
              value: "$DB_CA_CERT"
            - name: DB_USER
              value: "$DB_USER"
            - name: DB_PASS
              value: "$DB_PASS"
            - name: DB_TYPE
              value: "$DB_TYPE"
            - name: DB_NAME
              value: "$DB_NAME"
            - name: DB_PORT
              value: "$DB_PORT"
            - name: REDIS_ENABLED
              value: "$REDIS_ENABLED"
            - name: REDIS_URL
              value: "$REDIS_URL"
            - name: AWS_ACCESS_KEY_ID
              value: "$AWS_ACCESS_KEY_ID"
            - name: AWS_SECRET_ACCESS_KEY
              value: "$AWS_SECRET_ACCESS_KEY"
            - name: AWS_REGION
              value: "$AWS_REGION"
            - name: AWS_S3_BUCKET
              value: "$AWS_S3_BUCKET"
            - name: WASABI_ACCESS_KEY_ID
              value: "$WASABI_ACCESS_KEY_ID"
            - name: WASABI_SECRET_ACCESS_KEY
              value: "$WASABI_SECRET_ACCESS_KEY"
            - name: WASABI_REGION
              value: "$WASABI_REGION"
            - name: WASABI_SERVICE_URL
              value: "$WASABI_SERVICE_URL"
            - name: WASABI_S3_BUCKET
              value: "$WASABI_S3_BUCKET"
            - name: EXPRESS_SESSION_SECRET
              value: "$EXPRESS_SESSION_SECRET"
            - name: JWT_SECRET
              value: "$JWT_SECRET"
            - name: JWT_REFRESH_TOKEN_SECRET
              value: "$JWT_REFRESH_TOKEN_SECRET"
            - name: JWT_REFRESH_TOKEN_EXPIRATION_TIME
              value: "$JWT_REFRESH_TOKEN_EXPIRATION_TIME"
            - name: CLOUDINARY_API_KEY
              value: "$CLOUDINARY_API_KEY"
            - name: CLOUDINARY_API_SECRET
              value: "$CLOUDINARY_API_SECRET"
            - name: CLOUDINARY_CLOUD_NAME
              value: "$CLOUDINARY_CLOUD_NAME"
            - name: DEFAULT_CURRENCY
              value: "USD"
            - name: MAIL_FROM_ADDRESS
              value: "$MAIL_FROM_ADDRESS"
            - name: MAIL_HOST
              value: "$MAIL_HOST"
            - name: MAIL_PORT
              value: "$MAIL_PORT"
            - name: MAIL_USERNAME
              value: "$MAIL_USERNAME"
            - name: MAIL_PASSWORD
              value: "$MAIL_PASSWORD"
            - name: ALLOW_SUPER_ADMIN_ROLE
              value: "$ALLOW_SUPER_ADMIN_ROLE"
            - name: GOOGLE_CLIENT_ID
              value: "$GOOGLE_CLIENT_ID"
            - name: GOOGLE_CLIENT_SECRET
              value: "$GOOGLE_CLIENT_SECRET"
            - name: GOOGLE_CALLBACK_URL
              value: "$GOOGLE_CALLBACK_URL"
            - name: FACEBOOK_CLIENT_ID
              value: "$FACEBOOK_CLIENT_ID"
            - name: FACEBOOK_CLIENT_SECRET
              value: "$FACEBOOK_CLIENT_SECRET"
            - name: FACEBOOK_GRAPH_VERSION
              value: "$FACEBOOK_GRAPH_VERSION"
            - name: FACEBOOK_CALLBACK_URL
              value: "$FACEBOOK_CALLBACK_URL"
            - name: INTEGRATED_USER_DEFAULT_PASS
              value: "$INTEGRATED_USER_DEFAULT_PASS"
            - name: UPWORK_REDIRECT_URL
              value: "$UPWORK_REDIRECT_URL"
            - name: FILE_PROVIDER
              value: "$FILE_PROVIDER"
            - name: GAUZY_AI_GRAPHQL_ENDPOINT
              value: "$GAUZY_AI_GRAPHQL_ENDPOINT"
            - name: GAUZY_AI_REST_ENDPOINT
              value: "$GAUZY_AI_REST_ENDPOINT"
            - name: UNLEASH_APP_NAME
              value: "$UNLEASH_APP_NAME"
            - name: UNLEASH_API_URL
              value: "$UNLEASH_API_URL"
            - name: UNLEASH_INSTANCE_ID
              value: "$UNLEASH_INSTANCE_ID"
            - name: UNLEASH_REFRESH_INTERVAL
              value: "$UNLEASH_REFRESH_INTERVAL"
            - name: UNLEASH_METRICS_INTERVAL
              value: "$UNLEASH_METRICS_INTERVAL"
            - name: UNLEASH_API_KEY
              value: "$UNLEASH_API_KEY"
            - name: PM2_PUBLIC_KEY
              value: "$PM2_PUBLIC_KEY"
            - name: PM2_SECRET_KEY
              value: "$PM2_SECRET_KEY"
            - name: PM2_MACHINE_NAME
              value: "$PM2_MACHINE_NAME"
            - name: JITSU_SERVER_URL
              value: "$JITSU_SERVER_URL"
            - name: JITSU_SERVER_WRITE_KEY
              value: "$JITSU_SERVER_WRITE_KEY"
            - name: OTEL_ENABLED
              value: "$OTEL_ENABLED"
            - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
              value: "$OTEL_EXPORTER_OTLP_TRACES_ENDPOINT"
            - name: OTEL_EXPORTER_OTLP_HEADERS
              value: "$OTEL_EXPORTER_OTLP_HEADERS"
            - name: GAUZY_GITHUB_CLIENT_ID
              value: "$GAUZY_GITHUB_CLIENT_ID"
            - name: GAUZY_GITHUB_CLIENT_SECRET
              value: "$GAUZY_GITHUB_CLIENT_SECRET"
            - name: GAUZY_GITHUB_WEBHOOK_URL
              value: "$GAUZY_GITHUB_WEBHOOK_URL"
            - name: GAUZY_GITHUB_WEBHOOK_SECRET
              value: "$GAUZY_GITHUB_WEBHOOK_SECRET"
            - name: GAUZY_GITHUB_APP_PRIVATE_KEY
              value: "$GAUZY_GITHUB_APP_PRIVATE_KEY"
            - name: GAUZY_GITHUB_APP_ID
              value: "$GAUZY_GITHUB_APP_ID"
            - name: GAUZY_GITHUB_APP_NAME
              value: "$GAUZY_GITHUB_APP_NAME"
            - name: GAUZY_GITHUB_POST_INSTALL_URL
              value: "$GAUZY_GITHUB_POST_INSTALL_URL"
            - name: GAUZY_GITHUB_OAUTH_CLIENT_ID
              value: "$GAUZY_GITHUB_OAUTH_CLIENT_ID"
            - name: GAUZY_GITHUB_OAUTH_CLIENT_SECRET
              value: "$GAUZY_GITHUB_OAUTH_CLIENT_SECRET"
            - name: GAUZY_GITHUB_OAUTH_CALLBACK_URL
              value: "$GAUZY_GITHUB_OAUTH_CALLBACK_URL"
            - name: MAGIC_CODE_EXPIRATION_TIME
              value: "$MAGIC_CODE_EXPIRATION_TIME"
            - name: APP_NAME
              value: "$APP_NAME"
            - name: APP_LOGO
              value: "$APP_LOGO"
            - name: APP_SIGNATURE
              value: "$APP_SIGNATURE"
            - name: APP_LINK
              value: "$APP_LINK"
            - name: APP_EMAIL_CONFIRMATION_URL
              value: "$APP_EMAIL_CONFIRMATION_URL"
            - name: APP_MAGIC_SIGN_URL
              value: "$APP_MAGIC_SIGN_URL"
            - name: COMPANY_LINK
              value: "$COMPANY_LINK"
            - name: COMPANY_NAME
              value: "$COMPANY_NAME"
          ports:
            - containerPort: 3000
              protocol: TCP
