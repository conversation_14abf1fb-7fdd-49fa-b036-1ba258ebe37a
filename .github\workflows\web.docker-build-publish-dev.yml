name: Build and Publish Web App Docker Images Dev

on:
  push:
    branches: [develop]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  ever-cloc-webapp:
    runs-on: buildjet-8vcpu-ubuntu-2204

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          repository: 'ever-co/ever-teams'
          ref: develop

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Output NEXT_PUBLIC_GAUZY_API_SERVER_URL
        run: echo "NEXT_PUBLIC_GAUZY_API_SERVER_URL=${{ secrets.NEXT_PUBLIC_GAUZY_API_SERVER_URL }}"

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          load: true
          tags: |
            ghcr.io/ever-co/ever-cloc-webapp-dev:latest
            registry.digitalocean.com/ever/ever-cloc-webapp-dev:latest
          cache-from: type=registry,ref=ghcr.io/ever-co/ever-cloc-webapp-dev:latest
          cache-to: type=inline
          build-args: |
            NODE_ENV=production
            NEXT_PUBLIC_GAUZY_API_SERVER_URL=${{ secrets.NEXT_PUBLIC_GAUZY_API_SERVER_URL }}
            NEXT_PUBLIC_GA_MEASUREMENT_ID=${{ secrets.NEXT_PUBLIC_GA_MEASUREMENT_ID }}
            NEXT_PUBLIC_CAPTCHA_SITE_KEY=${{ secrets.NEXT_PUBLIC_CAPTCHA_SITE_KEY }}
            NEXT_PUBLIC_DISABLE_AUTO_REFRESH=false
            NEXT_PUBLIC_COOKIE_DOMAINS=${{ secrets.NEXT_PUBLIC_COOKIE_DOMAINS }}
            NEXT_PUBLIC_BOARD_APP_DOMAIN=${{ secrets.NEXT_PUBLIC_BOARD_APP_DOMAIN }}
            NEXT_PUBLIC_BOARD_BACKEND_POST_URL=${{ secrets.NEXT_PUBLIC_BOARD_BACKEND_POST_URL }}
            NEXT_PUBLIC_BOARD_FIREBASE_CONFIG=${{ secrets.NEXT_PUBLIC_BOARD_FIREBASE_CONFIG }}
            NEXT_PUBLIC_MEET_DOMAIN=${{ secrets.NEXT_PUBLIC_MEET_DOMAIN }}
            NEXT_PUBLIC_SENTRY_DSN=${{ secrets.NEXT_PUBLIC_SENTRY_DSN }}
            NEXT_PUBLIC_SENTRY_DEBUG=${{ secrets.NEXT_PUBLIC_SENTRY_DEBUG }}
            NEXT_PUBLIC_JITSU_BROWSER_URL=${{ secrets.NEXT_PUBLIC_JITSU_BROWSER_URL }}
            NEXT_PUBLIC_JITSU_BROWSER_WRITE_KEY=${{ secrets.NEXT_PUBLIC_JITSU_BROWSER_WRITE_KEY }}
            NEXT_PUBLIC_GITHUB_APP_NAME=ever-github
            NEXT_PUBLIC_CHATWOOT_API_KEY=${{ secrets.NEXT_PUBLIC_CHATWOOT_API_KEY }}

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        run: doctl registry login --expiry-seconds 3600

      - name: Push to DigitalOcean Registry
        run: |
          docker push registry.digitalocean.com/ever/ever-cloc-webapp-dev:latest

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GH_TOKEN }}

      - name: Push to Github Registry
        run: |
          docker push ghcr.io/ever-co/ever-cloc-webapp-dev:latest
